@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-lightbulb-o"></i> Category 7: Research Proposals (Progress Report)
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category7aForm" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Instructions -->
        <div class="alert alert-info">
          <h5><i class="icon fa fa-info"></i> Progress Report</h5>
          Please provide a progress report for your ongoing research proposal.
        </div>

        <!-- Original Proposal Reference -->
        <div class="form-group row">
          <label for="originalProposalRef" class="col-sm-3 col-form-label">
            Original Proposal Reference <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="originalProposalRef" name="original_proposal_ref"
                   placeholder="Enter the reference number of your original proposal" required>
          </div>
        </div>

        <!-- Proposal Title -->
        <div class="form-group row">
          <label for="proposalTitle" class="col-sm-3 col-form-label">
            Proposal Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="proposalTitle" name="proposal_title" rows="2"
                      placeholder="Enter the title of your research proposal" required></textarea>
          </div>
        </div>

        <!-- Progress Period -->
        <div class="form-group row">
          <label for="progressPeriod" class="col-sm-3 col-form-label">
            Progress Period <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <div class="row">
              <div class="col-sm-6">
                <input type="date" class="form-control" id="progressStartDate" name="progress_start_date" required>
                <small class="form-text text-muted">Period Start Date</small>
              </div>
              <div class="col-sm-6">
                <input type="date" class="form-control" id="progressEndDate" name="progress_end_date" required>
                <small class="form-text text-muted">Period End Date</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Progress Percentage -->
        <div class="form-group row">
          <label for="progressPercentage" class="col-sm-3 col-form-label">
            Progress Percentage <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <div class="input-group">
              <input type="number" class="form-control" id="progressPercentage" name="progress_percentage"
                     min="0" max="100" placeholder="Enter progress percentage" required>
              <div class="input-group-append">
                <span class="input-group-text">%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Objectives Achieved -->
        <div class="form-group row">
          <label for="objectivesAchieved" class="col-sm-3 col-form-label">
            Objectives Achieved <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="objectivesAchieved" name="objectives_achieved" rows="4"
                      placeholder="Describe the objectives that have been achieved" required></textarea>
          </div>
        </div>

        <!-- Methodology Progress -->
        <div class="form-group row">
          <label for="methodologyProgress" class="col-sm-3 col-form-label">
            Methodology Progress <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="methodologyProgress" name="methodology_progress" rows="4"
                      placeholder="Describe the progress in methodology implementation" required></textarea>
          </div>
        </div>

        <!-- Challenges Faced -->
        <div class="form-group row">
          <label for="challengesFaced" class="col-sm-3 col-form-label">
            Challenges Faced <small>(Optional)</small>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="challengesFaced" name="challenges_faced" rows="3"
                      placeholder="Describe any challenges faced during this period"></textarea>
          </div>
        </div>

        <!-- Next Steps -->
        <div class="form-group row">
          <label for="nextSteps" class="col-sm-3 col-form-label">
            Next Steps <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="nextSteps" name="next_steps" rows="3"
                      placeholder="Describe the planned next steps" required></textarea>
          </div>
        </div>

        <!-- Progress Report Document Upload -->
        <div class="form-group row">
          <label for="progressReportDocument" class="col-sm-3 col-form-label">
            Progress Report Document <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="progressReportDocument" name="progress_report_document"
                   accept=".pdf,.doc,.docx" required>
            <small class="form-text text-muted">Upload detailed progress report (PDF, DOC, DOCX) - Max: 20MB</small>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 7 (Progress Report)
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category7aForm').reset();
}
</script>

@endsection
