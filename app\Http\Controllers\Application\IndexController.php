<?php

namespace App\Http\Controllers\Application;

use App\Http\Controllers\Controller;
use App\Models\Application;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    public function __construct()
    {
        $currentURL = url()->current();
        session()->put('special_callback_url', $currentURL);
        session()->put('special_value', false);
        session()->put('checkReturn',  true);
        $this->middleware('auth');
        $this->middleware('role:applicant');
    }

    public function index()
    {
        session()->put('special_value', true);
        session()->forget('special_callback_url');
        $empNo = Auth()->user()->empNo;
        $name = Auth()->user()->name;
        $year = now()->format('Y');


        //check for unprocessing appplication
        $unprocessedApplicationCount = Application::where('empID', $empNo)->where('year', $year)->where('submittedStatus', 1)->where('applicationFinalDecision', 0)->count();

        //check for approved appplication
        $completedApplicationCount = Application::where('empID', $empNo)->where('year', $year)->where('submittedStatus', 1)->where('applicationFinalDecision', 1)->count();




        return view('frontend.application_new.index', compact('empNo', 'name', 'year'));
    }
}
