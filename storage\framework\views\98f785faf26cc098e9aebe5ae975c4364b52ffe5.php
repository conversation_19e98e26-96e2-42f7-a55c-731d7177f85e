<div class="panel panel-default" data-bs-theme="dark">
    <a class="toggleLinks" data-toggle="collapse" data-parent="#accordion" href="#collapse1">
        <div class="panel-heading">
            <h5 class="panel-title" id="cat1PanelTitle">
            Category 1: Indexed Journal Publications
            </h5>
        </div>
    </a>

<div id="collapse1" class="panel-collapse collapse">  <!-- in -->
    <div class="panel-body">
        <!-- *******************Category one form start****************** -->

        <form style="display: <?php echo $partOneData !== null ? "none" : "block" ?>;" id="cat1form">
            <input type="hidden" name="partOneLastRowId" id="partOneLastRowId">
            <?php echo csrf_field(); ?>
        <!-- 1.1 -->
        <fieldset class="form-group">
            <div class="row">
                <label for="lable" class="col-sm-2 col-form-label">Indexing Service <span class="asterisk">*</span></label>
                <div class="col-sm-10">
                    <div class="form-check">
                    <input class="form-check-input" type="radio" name="indexingService" id="service1" value="Web of Science" checked>
                    <label class="form-check-label" for="service1">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Web of Science
                    </label>
                    </div>
                    <div class="form-check">
                    <input class="form-check-input" type="radio" name="indexingService" id="service2" value="SCOPUS">
                    <label class="form-check-label" for="service2">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SCOPUS
                    </label>
                    </div>
                    <div class="form-check">
                    <input class="form-check-input" type="radio" name="indexingService" id="service3" value="Emerald Insight">
                    <label class="form-check-label" for="service3">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Emerald Insight
                    </label>
                    </div>
                    <!-- <div class="form-check">
                    <input class="form-check-input" type="radio" name="indexingService" id="service4" value="Cabell's International">
                    <label class="form-check-label" for="service4">
                        Cabell's International
                    </label>
                    </div> -->
                </div>
            </div>
        </fieldset>
        <!-- 1.2 -->
        <div class="form-group row">
            <label for="inputTitle" class="col-sm-2 col-form-label">Title <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="text" name="title" class="form-control" oninput="validateCategory01()" id="inputTitle" placeholder="Title" required>
                <p id="titleErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter the title* </p>
            </div>

        </div>
        <!-- 1.3 -->
        <div class="form-group row">
            <label for="inputJournalName" class="col-sm-2 col-form-label">Journal Name <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="text" name="journalName" class="form-control" oninput="validateCategory01()" id="inputJournalName" placeholder="Journal Name" required>
                <p id="journalNameErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter the Journal Name* </p>
            </div>
        </div>
        <!-- 1.4  Acceptance-->
        <div class="form-group row" style="display: none;">
            <label for="checkPublication" class="col-sm-2 col-form-label">Status <span class="asterisk">*</span></label>
            <div class="col-sm-2">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="cat1Status" id="checkAccepted" value="1">
                <label class="form-check-label" for="gridCheck1" >
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Accepted
                </label>
            </div>
            </div>
            <!-- 1.4 Publication -->
            <div class="col-sm-2">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="cat1Status" id="checkPublication" value="2" checked>
                <label class="form-check-label" for="gridCheck1">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Published
                </label>
            </div>
            </div>
            <p id="cat1AtLeastOne" style="display:none" class="allInputErrorMsg"> *You must select at least one before adding* </p>
        </div>
        <!-- 1.5 Acceptance -->
        <!-- <div class="form-group row" style="margin-bottom: 0px;">
            <label for="inputAcceptDate" class="col-sm-2 col-form-label"></label>
            <label for="inputAcceptDate" class="col-sm-2 col-form-label" id="acceptDateLable">Date of Acceptance <span class="asterisk">*</span></label>
            <div class="col-sm-8">
                <input type="date" name="dateOfAcceptance" class="form-control" oninput="validateCategory01()" id="inputAcceptDate">
                <p id="acceptDateErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter Date of Acceptance* </p>
            </div>
        </div> -->
        <!-- 1.4 Publication -->
        <!-- <div class="form-group row">
            <label for="checkPublication" class="col-sm-2 col-form-label" ></label>
            <div class="col-sm-10">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="cat1Status" id="checkPublication">
                <label class="form-check-label" for="gridCheck1">
                Published
                </label>
            </div>
            </div>
        </div> -->
        <!-- 1.5 Publication -->
        <div class="form-group row" style="margin-bottom: 15px;">
            <!-- <label for="inputPublicationYear" class="col-sm-2 col-form-label"></label> -->
            <label for="inputPublicationYear" class="col-sm-2 col-form-label" id="publicationYearLable">Year of Publication <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="number" name="yearOfPublication" class="form-control" oninput="validateCategory01()" id="inputPublicationYear" placeholder="Year">
                <p id="publicationYearInfoErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter Year of Publication* </p>
            </div>
        </div>
        <!-- 1.5 Publication -->
        <div class="form-group row" style="margin-bottom: 5px;">
            <!-- <label for="inputPublication" class="col-sm-2 col-form-label"></label> -->
            <label for="inputPublication" class="col-sm-2 col-form-label" id="publicationLable">Publication Volume, Issue and Page/s <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="text" name="yearOfPublicationIssue" class="form-control" oninput="validateCategory01()" id="inputPublication" placeholder="Volume (Issue), Page/s ">
                <p id="publicationInfoErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter Publication Issue, Volume and Pages* </p>
            </div>
        </div>
        <!-- 1.6 -->
        <div class="form-group row" style="margin-bottom: 15px;">
            <!-- <label for="inputURL" class="col-sm-2 col-form-label"></label> -->
            <label for="inputURL" id="cat1UrlLable" class="col-sm-2 col-form-label">URL/ DOI of the article <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="url" name="relevantURL" class="form-control" oninput="validateCategory01()" id="inputRelevantURL" placeholder="Relevant URL">
                <p id="cat1URLErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter the Relevant URL or upload a .pdf file* </p>
            </div>
        </div>
        <!-- 1.7 -->
        <div class="form-group row">
            <!-- <label for="inputURL" class="col-sm-2 col-form-label"></label> -->
            <label for="inputFile" id="cat1fileUploadLable" class="col-sm-2 col-form-label">Upload the relevant full article/ Approved Letter issued by the Research Council <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="file" name="uploadFile" oninput="validatePDFCategory01()" class="form-control" id="cat1UploadFile" accept=".pdf">
                <p id="cat1FileUploadErrorMsg" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
            </div>
        </div>
        <div class="form-group row">
            <!-- <label for="inputURL" class="col-sm-2 col-form-label"></label> -->
            <label for="inputEvidenceFile" id="cat1EvidenceFileUploadLable" class="col-sm-2 col-form-label">Upload the evidence for the relevant indexing service/s<span class="asterisk">*</span>
            (<a href="<?php echo e(asset('Example/Indexing interface samples.pdf')); ?>" target="_blank">Sample Evidence File</a>)
            </label>
            <div class="col-sm-10">
                <input type="file" name="uploadFile" oninput="validateEvidencePDFCategory01()" class="form-control" id="cat1EvidenceUploadFile" accept=".pdf">
                <p id="cat1EvidenceFileUploadErrorMsg" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
            </div>
        </div>

        <div class="form-group row">
            <!-- Category one draft button -->
            <div class="col-sm-6 col-md-10">
                <!-- <button type="submit" name="categoryOneSubmit" class="btn btn-primary">Save Draft</button> -->
            </div>
            <!-- Category one add button -->
            <div class="col-sm-3 col-md-1">
                <button type="button" onclick="AddPartOneRecoredToDb()" id="categoryOneAddBtn" name="categoryOneAdd" class="btn btn-primary add-btn">Submit</button>
            </div>
            <!-- Category One Clear button -->
            <div class="col-sm-3 col-md-1">
                <button type="button" class="btn btn-warning clear-btn" onclick="cat1Clear()">Clear</button>
            </div>
        </div>
        </form>
        <!-- *******************form end******************** -->
        <!-- Table 1 start  currently not visible-->
        <div class="form-group row" style="overflow-x:auto; display:none;">
            <table id="catagoryOneTable" class="table table-bordered">
                <thead>
                <tr>
                    <th scope="col">No</th>
                    <th scope="col">Service</th>
                    <th scope="col">Title</th>
                    <th scope="col">Journal Name</th>
                    <th scope="col">Status</th>
                    <!-- <th scope="col">Date of Acceptence</th> -->
                    <th scope="col">Publication Summary</th>
                    
                    <th scope="col">File</th>
                    <th scope="col">Evidence File</th>
                    <th scope="col">Action</th>
                </tr>
                </thead>
                <tbody>
                    <?php $partonerowcount = 0; ?>
                    <?php if($partOneData !== null): ?>
                        <?php $__currentLoopData = $partOneData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php $partonerowcount = ++$partonerowcount; ?>
                            <tr id="partone<?php echo e($index+1); ?>">
                                <td><?php echo e($index+1); ?></td>
                                <td><?php echo e($row->indexingService); ?></td>
                                <td><?php echo e($row->title); ?></td>
                                <td><?php echo e($row->journalName); ?></td>
                                <td>
                                    <?php if($row->status == 1): ?>
                                    Accepted
                                    <?php elseif($row->status == 2): ?>
                                    Published
                                    <?php endif; ?>
                                </td>
                                <!-- <td><?php echo e($row->dateOfAcceptance); ?></td> -->
                                <td>
                                    <?php if($row->status == 2): ?>
                                    <b>Year of Publication : </b><?php echo e($row->yearOfPublication); ?> <br><b> Volume, Issue and Page/s: </b> <?php echo e($row->volumeIssuePages); ?>

                                    <br><b>Relavent URL : </b><a href="<?php echo e($row->relevantURL); ?>" target=”_blank”><?php echo e($row->relevantURL); ?></a>
                                    <?php endif; ?>
                                </td>
                                
                                <td>
                                    <?php if($row->uploadFile): ?>
                                    <span class="badge badge-pill badge-success">Uploaded</span>
                                    <?php else: ?>
                                    <span class="badge badge-pill badge-dark">No Attachment</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($row->evidanceFile): ?>
                                    <span class="badge badge-pill badge-success">Uploaded</span>
                                    <?php else: ?>
                                    <span class="badge badge-pill badge-dark">No Attachment</span>
                                    <?php endif; ?>
                                </td>
                                <td><button class="btn btn-sm btn-danger" onclick="deleteDBrowPart1('<?php echo e($row->id); ?>','<?php echo e($index+1); ?>','1')">Delete</button></td>
                            </tr>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <?php endif; ?>

                    <!-- data will be add -->
                </tbody>
            </table>
        </div>
        <!-- Table 1 end -->

    </div>
  </div>
</div>

<!-- Publication year validation -->
 <script>


 </script>

<!-- Category one 1.5 acceptance -->
<script>

    // Get the radio and input field elements
    // const checkAccepted = document.getElementById('checkAccepted');
    // const publicationCheckbox = document.getElementById('checkPublication');
    // const inputField = document.getElementById('inputAcceptDate');
    // const acceptLable = document.getElementById('acceptDateLable');
    // const publicationInputField = document.getElementById('inputPublication');
    // const publicationLable = document.getElementById('publicationLable');
    // const publicationYearInputField = document.getElementById('inputPublicationYear');
    // const publicationYearLable = document.getElementById('publicationYearLable');
    // const cat1UrlLable = document.getElementById('cat1UrlLable');
    // const cat1URL = document.getElementById('inputRelevantURL');
    // const cat1File = document.getElementById('cat1UploadFile');
    // const cat1fileUploadLable = document.getElementById('cat1fileUploadLable');

    // Category one 1.5 acceptance
    // Add a change event listener to the checkAccepted
    // checkAccepted_delete.addEventListener('change', function() {

    //     // If the checkAccepted is checked, make the input field visible
    //     if (checkAccepted.checked) {
    //         inputField.style.display = 'block';
    //         acceptLable.style.display = 'block';
    //         cat1File.style.display = 'block';
    //         cat1fileUploadLable.style.display = 'block';
    //         cat1fileUploadLable.innerHTML = 'Upload the Acceptance Letter <span class="asterisk">*</span>';

    //         // Otherwise, hide the input field
    //         publicationInputField.style.display = 'none';
    //         publicationLable.style.display = 'none';
    //         publicationYearInputField.style.display = 'none';
    //         publicationYearLable.style.display = 'none';
    //         cat1UrlLable.style.display = 'none';
    //         cat1URL.style.display = 'none';

    //         // Clear hidden values
    //         publicationInputField.value = '';
    //         cat1URL.value = '';
    //         cat1File.value = '';

    //         // Clear error msg
    //         document.getElementById("publicationInfoErrorMsg").style.display = "none";
    //         document.getElementById("cat1URLErrorMsg").style.display = "none";

    //     } else {

    //         document.getElementById("acceptDateErrorMsg").style.display = "none";
    //     }
    // });

    // Category one 1.6 publication
    // Add a change event listener to the checkbox

    // publicationCheckbox_delete.addEventListener('change', function() {
    // // If the checkbox is checked, make the input field visible
    //     if (publicationCheckbox.checked) {
    //         publicationInputField.style.display = 'block';
    //         publicationLable.style.display = 'block';
    //         publicationYearInputField.style.display = 'block';
    //         publicationYearLable.style.display = 'block';
    //         cat1UrlLable.style.display = 'block';
    //         cat1URL.style.display = 'block';
    //         cat1File.style.display = 'block';
    //         cat1fileUploadLable.style.display = 'block';
    //         cat1fileUploadLable.innerHTML = 'Upload the relevant full article/ Acceptance Letter issued by the Research Council';

    //         // Otherwise, hide the input field
    //         inputField.style.display = 'none';
    //         acceptLable.style.display = 'none';

    //         // Clear hidden values
    //         inputField.value = '';
    //         cat1File.value = '';

    //         // Clear error msg
    //         document.getElementById("acceptDateErrorMsg").style.display = "none";
    //     } else {

    //         document.getElementById("publicationInfoErrorMsg").style.display = "none";
    //     }
    // });

    // Hide the input field by default
    // inputField.style.display = 'none';
    // acceptLable.style.display = 'none';
    // publicationInputField.style.display = 'none';
    // publicationLable.style.display = 'none';
    // publicationYearInputField.style.display = 'none';
    // publicationYearLable.style.display = 'none';
    // cat1UrlLable.style.display = 'none';
    // cat1URL.style.display = 'none';
    // cat1File.style.display = 'none';
    // cat1fileUploadLable.style.display = 'none';

</script>

<!-- Category one lower table -->

<script>

    let rowCount = 0;
    var partOneCount = '<?php echo e($partOneCount); ?>';

    function AddRowPartOne(insertedId){
            const service = document.querySelector('input[name="indexingService"]:checked').value;
            const title = document.getElementById("inputTitle").value;
            const journalName = document.getElementById("inputJournalName").value;
            // const isAccepted = document.getElementById("checkAccepted").checked;
            const cat1Status = document.querySelector('input[name="cat1Status"]:checked').value;
            // const acceptedDate = isAccepted ? document.getElementById("inputAcceptDate").value : "";
            // const isPublished = document.getElementById("checkPublication").checked;
            const publicationYearInfo =  document.getElementById("inputPublicationYear").value;
            const publicationInfo =  document.getElementById("inputPublication").value;
            const url = document.getElementById("inputRelevantURL").value;
            const file = document.getElementById("cat1UploadFile").value;
            const evidenceFile = document.getElementById("cat1EvidenceUploadFile").value;
            rowCount++;
            partOneCount++;

            if(validateCategory01()){

                // Create a new row
                const newRow = catagoryOneTable.insertRow();

                // Add data to the row
                newRow.insertCell(0).innerHTML = partOneCount;
                newRow.insertCell(1).innerHTML = service;
                newRow.insertCell(2).innerHTML = title;
                newRow.insertCell(3).innerHTML = journalName;

                newRow.insertCell(4).innerHTML = cat1Status == 1 ? 'Accepted' : 'Published';
                // newRow.insertCell(5).innerHTML = acceptedDate;

                newRow.insertCell(5).innerHTML = '<b>Year of Publication : </b>'+publicationYearInfo+'<br><b> Volume, Issue and Page/s: </b>'+publicationInfo + '<br><b> Relavent URL : </b><a href="'+ url +'" target=”_blank”>'+url+'</a>' ;

                //newRow.insertCell(7).innerHTML = url;
                newRow.insertCell(6).innerHTML = file ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';
                newRow.insertCell(7).innerHTML = evidenceFile ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';

                // Add delete button to the row with Bootstrap 'btn-danger' class
                const deleteButtonCell = newRow.insertCell(8);
                const deleteButton = document.createElement("button");
                deleteButton.textContent = "Delete";
                deleteButton.className = "btn btn-sm btn-danger";

                deleteButton.addEventListener("click", function () {
                    deleteDBrowPart1(insertedId,newRow,0);
                });
                deleteButtonCell.appendChild(deleteButton);

                // Clear form fields
                document.getElementById("inputTitle").value = "";
                document.getElementById("inputJournalName").value = "";
                // document.getElementById("checkAccepted").checked = false;
                // document.getElementById("inputAcceptDate").value = "";
                // document.getElementById("checkPublication").checked = false;
                document.getElementById("inputPublication").value = "";
                document.getElementById("inputPublicationYear").value = "";
                document.getElementById("inputRelevantURL").value = "";
                document.getElementById("cat1UploadFile").value = "";
                document.getElementById("cat1EvidenceUploadFile").value = "";
                document.getElementById('cat1form').style.display = 'none';
                // alert("vali");
                // inputField.style.display = 'none';
                // acceptLable.style.display = 'none';
                // publicationInputField.style.display = 'none';
                // publicationLable.style.display = 'none';
                // publicationYearInputField.style.display = 'none';
                // publicationYearLable.style.display = 'none';
                // cat1UrlLable.style.display = 'none';
                // cat1URL.style.display = 'none';
                // cat1File.style.display = 'none';
                // cat1fileUploadLable.style.display = 'none';

            }

           //document.getElementById("partOneLastRowId").value = rowCount;
    }


    // Function to delete a row from the table
    function cat1DeleteRow(row,accNo) {
        if(accNo == '1'){
            var rowTemp = document.getElementById("partone"+row);
            rowTemp.parentNode.removeChild(rowTemp);

        }else{
            catagoryOneTable.deleteRow(row.rowIndex);
        }

        // Open the Relevant category
        document.getElementById('cat1form').style.display = 'block';

        // collapse - enable
        var panelTitleID = 'cat1PanelTitle';
        resetAllPanel(panelTitleID);

    }

    // Delete in the row of database
    function deleteDBrowPart1(deletedId,tblRowId, accNo){
         //Catagory One - Ajax request for delete row
        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
            $.ajax({

                url: '/deletePartOneRecord',
                type: 'POST',
                data: {_token: CSRF_TOKEN,
                    deletedId: deletedId,
                },
                dataType: 'JSON',

                success: function (data) {
                    if(data.status){
                        cat1DeleteRow(tblRowId,accNo);
                    }else{
                        alert("Error1");
                    }

                },
                error: function(xhr, status, error) {
                    alert("Somethig went worng");
                }

            });
    }

    function AddPartOneRecoredToDb(){
        if(validateCategory01()){
            const service = document.querySelector('input[name="indexingService"]:checked').value;
            const title = document.getElementById("inputTitle").value;
            const journalName = document.getElementById("inputJournalName").value;
            const cat1Status = document.querySelector('input[name="cat1Status"]:checked').value;
            // const isAccepted = document.getElementById("checkAccepted").checked;
            // const acceptedDate = isAccepted ? document.getElementById("inputAcceptDate").value : "";
            // const isPublished = document.getElementById("checkPublication").checked;
            const acceptedDate = "";
            const publicationYearInfo = document.getElementById("inputPublicationYear").value;
            const publicationInfo = document.getElementById("inputPublication").value;
            const url = document.getElementById("inputRelevantURL").value;
            const file = document.getElementById("cat1UploadFile").value;
            const evidenceFile = document.getElementById("cat1EvidenceUploadFile").value;
            // get values for value is set or not
            const panelTitle = document.getElementById('cat1PanelTitle');
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');
            // Get elements by class name
            var toggleLinks = document.getElementsByClassName('toggleLinks');

            var files = $('#cat1UploadFile')[0].files;
            var file2 = $('#cat1EvidenceUploadFile')[0].files;
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');

            // Add button disable
            var cat1AddBtn = document.getElementById('categoryOneAddBtn');
            // cat1AddBtn.disabled = true;

            var uploadedData = new FormData();
            uploadedData.append('indexingService',service);
            uploadedData.append('title',title);
            uploadedData.append('journalName',journalName);
            uploadedData.append('status',cat1Status);
            uploadedData.append('dateOfAcceptance',acceptedDate);
            uploadedData.append('yearOfPublication',publicationYearInfo);
            uploadedData.append('volumeIssuePages',publicationInfo);
            uploadedData.append('url',url);
            uploadedData.append('_token',CSRF_TOKEN);
            uploadedData.append('file',files[0]);
            uploadedData.append('file2',file2[0]);

        //   Catagory One - Ajax request
        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
        Swal.fire({
            title: "Are you sure that you want to submit?",
            text: "You will not be able to edit the application after submitting it",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes. Submit"

        }).then((result) => {
            if (result.isConfirmed) {
                // Ajax start
                $.ajax({
                        url: '/createPartOneRecord',
                        type: 'POST',
                        data: uploadedData,
                        contentType: false,
                        processData: false,
                        dataType: 'JSON',

                        success: function (data) {
                            if(data.status){
                                AddRowPartOne(data.insertedId);
                                panelTitle.style.color = '#021691';

                                // Loop through the elements and update the data-toggle attribute
                                for (var i = 0; i < toggleLinks.length; i++) {
                                    // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                                }

                                // Enable the submit and save draft button
                                submitButton.disabled = false;
                                saveDraftButton.disabled = false;

                                //  If successful, Enable the Add button
                                cat1AddBtn.disabled = false;
                                // Redirect to the application.store route
                                window.location.href = "<?php echo e(route('application.store')); ?>";
                            }

                        },
                        error: function(xhr, status, error) {
                            alert("Somethig went worng");
                        }

                        });
                // Ajax end
                    Swal.fire({
                    title: "Success!",
                    // text: "Your application has been submitted successfuly.",
                    icon: "success"
                });
            }
        });

        }

    }


    // Validate all input are empty or not
    function validateCategory01() {
            // Get values from form
            const service = document.querySelector('input[name="indexingService"]:checked').value;
            const title = document.getElementById("inputTitle").value;
            const journalName = document.getElementById("inputJournalName").value;
            // if(document.querySelector('input[name="cat1Status"]:checked') != null) {
            //     const cat1Status = document.querySelector('input[name="cat1Status"]:checked').value;
            // }else{
            //     const cat1Status = 0;
            // }
            // const isAccepted = document.getElementById("checkAccepted").checked;
            // const acceptedDate = isAccepted ? document.getElementById("inputAcceptDate").value : "";
            // const isPublished = document.getElementById("checkPublication").checked;

            const publicationYearInfo = document.getElementById("inputPublicationYear").value;
            const publicationInfo = document.getElementById("inputPublication").value;
            const url = document.getElementById("inputRelevantURL").value;
            const file = document.getElementById("cat1UploadFile").value;
            const evidenceFile = document.getElementById("cat1EvidenceUploadFile").value;

            // get values for value is set or not
            const panelTitle = document.getElementById('cat1PanelTitle');
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');

            // Get elements by class name
            var toggleLinks = document.getElementsByClassName('toggleLinks');

            // alert(title);
            //Validate Catgory 1 title
            var isValidate = true;
            if(title ==""){
                isValidate = false;
                document.getElementById("titleErrorMsg").style.display = "block";
            }else{
                document.getElementById("titleErrorMsg").style.display = "none";
            }

            //Validate Catagory 1 journal Name
            if(journalName ==""){
                isValidate = false;
                document.getElementById("journalNameErrorMsg").style.display = "block";
            }else{
                document.getElementById("journalNameErrorMsg").style.display = "none";
            }

            //Validate Catagory 1 check Accepted and show Acceptance date error msg
            // if(acceptedDate ==""){
            //     isValidate = false;
            //     document.getElementById("acceptDateErrorMsg").style.display = "block";
            // }else{
            //     document.getElementById("acceptDateErrorMsg").style.display = "none";
            // }

            //Validate Catagory 1 check Accepted and show upload file error msg
            if(file == ""){
                isValidate = false;
                document.getElementById("cat1FileUploadErrorMsg").style.display = "block";
            }else{
                document.getElementById("cat1FileUploadErrorMsg").style.display = "none";
            }


            //Validate Catagory 1 check Accepted and show upload evidence file error msg
            if(evidenceFile == ""){
                isValidate = false;
                document.getElementById("cat1EvidenceFileUploadErrorMsg").style.display = "block";
            }else{
                document.getElementById("cat1EvidenceFileUploadErrorMsg").style.display = "none";
            }


            //Validate Catagory 1 check Published and show publication info error msg
            if (publicationYearInfo =="") {
                isValidate = false;
                document.getElementById("publicationYearInfoErrorMsg").style.display = "block";
            } else {
                document.getElementById("publicationYearInfoErrorMsg").style.display = "none";
                const currentYear = new Date().getFullYear();
                const errorMsg = document.getElementById("publicationYearInfoErrorMsg");
                if (publicationYearInfo < currentYear - 2) {
                    isValidate = false;
                    errorMsg.style.display = "block";
                    errorMsg.textContent = `*The publication year must not exceed 2 years from the current year*`;
                } else {
                    errorMsg.style.display = "none";
                }
            }

            if (publicationInfo =="") {
                isValidate = false;
                document.getElementById("publicationInfoErrorMsg").style.display = "block";
            } else {
                document.getElementById("publicationInfoErrorMsg").style.display = "none";
            }
            //Validate Catagory 1 check Published and show Relevant URL error msg and show upload file error msg
            if (url == "") {
                isValidate = false;
                document.getElementById("cat1URLErrorMsg").style.display = "block";
            } else {
                document.getElementById("cat1URLErrorMsg").style.display = "none";
            }
            // if(checkPublication.checked){
            //     if (url == "" && file == "") {
            //         isValidate = false;
            //         document.getElementById("cat1URLErrorMsg").style.display = "block";
            //         document.getElementById("cat1FileUploadErrorMsg").style.display = "block";
            //     } else {
            //         document.getElementById("cat1URLErrorMsg").style.display = "none";
            //         document.getElementById("cat1FileUploadErrorMsg").style.display = "none";
            //     }
            // }

            // Either accepted or publication has been selected
            // if (checkPublication.checked == false && checkAccepted.checked == false) {
            //     isValidate = false;
            //     document.getElementById("cat1AtLeastOne").style.display = "block"
            // } else {
            //     document.getElementById("cat1AtLeastOne").style.display = "none"
            // }

            // alert(title);

            // check for the value is added or not
            if(title != "" || journalName != ""){
                // Change the text color of the panel title
                panelTitle.style.color = '#f2820a';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Disable the submit and save draft button
                submitButton.disabled = true;
                saveDraftButton.disabled = true;
            }else{
                // Reset the text color of the panel title
                panelTitle.style.color = '#021691';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Enable the submit button
                submitButton.disabled = false;
                saveDraftButton.disabled = false;
            }

            return isValidate;
    }

    // Validate upload only .pdf for Category One
    function validatePDFCategory01() {
        // Get the file input element
        var fileInput = document.getElementById('cat1UploadFile');

        // Get the selected file
        var selectedFile = fileInput.files[0];

        // Maximum file size in bytes (35MB)
        var maxSize = 35 * 1024 * 1024;

        // Check if a file is selected
        if (selectedFile) {
            // Get the file extension
            var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

            // Check if the file extension is not 'pdf'
            if (fileExtension !== 'pdf') {
                // Display error message for incorrect file type
                document.getElementById('cat1FileUploadErrorMsg').innerText = '*Please upload a .pdf file.*';
                document.getElementById('cat1FileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            }
            // Check if file size exceeds 35MB
            else if (selectedFile.size > maxSize) {
                // Display error message for file size exceeding 35MB
                document.getElementById('cat1FileUploadErrorMsg').innerText = '*File size should not exceed 35MB.*';
                document.getElementById('cat1FileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            }
            else {
                // Hide error message
                document.getElementById('cat1FileUploadErrorMsg').style.display = 'none';
            }
        }
    }

    // Validate upload only .pdf for Category One
    function validateEvidencePDFCategory01() {
        // Get the file input element
        var fileInput = document.getElementById('cat1EvidenceUploadFile');

        // Get the selected file
        var selectedFile = fileInput.files[0];

        // Maximum file size in bytes (35MB)
        var maxSize = 35 * 1024 * 1024;

        // Check if a file is selected
        if (selectedFile) {
            // Get the file extension
            var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

            // Check if the file extension is not 'pdf'
            if (fileExtension !== 'pdf') {
                // Display error message for incorrect file type
                document.getElementById('cat1EvidenceFileUploadErrorMsg').innerText = '*Please upload a .pdf file.*';
                document.getElementById('cat1EvidenceFileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            }
            // Check if file size exceeds 35MB
            else if (selectedFile.size > maxSize) {
                // Display error message for file size exceeding 35MB
                document.getElementById('cat1EvidenceFileUploadErrorMsg').innerText = '*File size should not exceed 35MB.*';
                document.getElementById('cat1EvidenceFileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            }
            else {
                // Hide error message
                document.getElementById('cat1EvidenceFileUploadErrorMsg').style.display = 'none';
            }
        }
    }


    // Category 1 Clear button
    function cat1Clear(){
            document.getElementById("inputTitle").value = '';
            document.getElementById("inputJournalName").value = '';
            // document.getElementById("inputAcceptDate").value = '';
            // document.getElementById("checkPublication").checked = false;
            // document.getElementById("checkAccepted").checked = false;
            document.getElementById("inputPublication").value = '';
            document.getElementById("inputRelevantURL").value = '';
            document.getElementById('cat1UploadFile').value = '';
            document.getElementById('inputPublicationYear').value = '';
            document.getElementById('cat1EvidenceUploadFile').value = '';
            // Clear error msg
            document.getElementById("titleErrorMsg").style.display = "none";
            document.getElementById("journalNameErrorMsg").style.display = "none";
            // document.getElementById("acceptDateErrorMsg").style.display = "none";
            document.getElementById("cat1FileUploadErrorMsg").style.display = "none";
            document.getElementById("publicationInfoErrorMsg").style.display = "none";
            document.getElementById("cat1URLErrorMsg").style.display = "none";
            // document.getElementById("cat1AtLeastOne").style.display = "none";
            document.getElementById("publicationYearInfoErrorMsg").style.display = "none";
            document.getElementById("cat1EvidenceFileUploadErrorMsg").style.display = "none";
            // Items to be checked are not visible
            // document.getElementById('inputAcceptDate').style.display = "none";
            // document.getElementById('acceptDateLable').style.display = "none";
            // document.getElementById('inputPublication').style.display = "none";
            // document.getElementById('publicationLable').style.display = "none";
            // document.getElementById('cat1UrlLable').style.display = "none";
            // document.getElementById('inputRelevantURL').style.display = "none";
            // document.getElementById('publicationYearLable').style.display = "none";
            // document.getElementById('inputPublicationYear').style.display = "none";
            // document.getElementById('cat1UploadFile').style.display = "none";
            // document.getElementById("cat1fileUploadLable").style.display = "none";

            //  When it clicks the clear button, Enable the Add button
            var cat1AddBtn = document.getElementById('categoryOneAddBtn');
            cat1AddBtn.disabled = false;

            var panelTitleID = 'cat1PanelTitle';
            resetAllPanel(panelTitleID);

        }
</script>
<?php /**PATH D:\Development\research_allowness\resources\views/frontend/application/component/category01.blade.php ENDPATH**/ ?>