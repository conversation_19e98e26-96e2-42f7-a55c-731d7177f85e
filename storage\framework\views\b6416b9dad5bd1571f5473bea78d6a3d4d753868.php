<div class="panel panel-default">
    <a class="toggleLinks" data-toggle="collapse" data-parent="#accordion" href="#collapse6">
        <div class="panel-heading">
            <h5 class="panel-title" id="cat6PanelTitle">
            Category 6: Postgraduate Studies
            </h5>
        </div>
    </a>
<div id="collapse6" class="panel-collapse collapse">
    <div class="panel-body">
    <!-- *******************Category six form start****************** -->
    <form style="display: <?php echo $partSixData !== null ? "none" : "block" ?>;" id="cat6form">
        <!-- 6.1 -->
        <fieldset class="form-group">
            <div class="row">
                <label for="lable" id="cat6UploadLable" class="col-sm-10 col-form-label">Upload a confirmation letter from Faculty of Graduate Studies/respective authority if you have registered for a postgraduate degree with a research component in the current year <span class="asterisk">*</span></label>
            </div>
        </fieldset>
        <div class="form-group row" id="cat6FileUploadSection">
            <div class="col-sm-10">
                <input type="file" name="cat6UploadFile" class="form-control" oninput="validatePDFCategory06()" id="cat6UploadFile" required accept=".pdf">
                <p id="cat6FileUploadErrorMsg" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
            </div>
            <!-- Category six draft button -->
            <div class="col-md-1">
                <button type="button" name="categorySixAdd" id="categorySixAddBtn" class="btn btn-primary add-btn" onclick="AddPartSixRecoredToDb()">Submit</button>
            </div>
            <!-- Category Six Clear button -->
            <div class="col-md-1">
                <button type="button" class="btn btn-warning clear-btn" id="categorySixClearBtn" onclick="cat6Clear()">Clear</button>
            </div>
        </div>
    </form>
    <!-- *******************form end******************** -->
    <!-- Table 6 start-->
    <div class="form-group row" style="overflow-x:auto; display:block;">
        <table id="categorySixTable" class="table table-bordered">
            <thead>
                <tr>
                    <th scope="col">No</th>
                    <th scope="col">File Name</th>
                    <th scope="col">Action</th>
                </tr>
                </thead>
                <tbody>
                    <?php $partSixrowcount = 0; ?>
                        <?php if($partSixData !== null): ?>
                            <?php $__currentLoopData = $partSixData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $partSixrowcount = ++$partSixrowcount; ?>
                                <tr id="partsix<?php echo e($index+1); ?>">
                                    <td><?php echo e($index+1); ?></td>
                                    <td>
                                        <?php if($row->uploadFile): ?>
                                            <span class="badge badge-pill badge-success">Uploaded</span>
                                            <?php else: ?>
                                            <span class="badge badge-pill badge-dark">No Attachment</span>
                                            <?php endif; ?>
                                    </td>
                                    <td><button class="btn btn-sm btn-danger" onclick="deleteDBrowPart6('<?php echo e($row->id); ?>','<?php echo e($index+1); ?>','1')">Delete</button></td>
                                </tr>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <?php endif; ?>
                    <!-- data will be add -->
                </tbody>
        </table>
    </div>
    <!-- Table 6 end -->
    </div>
  </div>
</div>

<!-- Catagory 6 lower table -->
<script>
    function cat6AddRow(insertedId) {

        // Validate category 6 and add new row
        // Get the file input element
        const fileName = document.getElementById('cat6UploadFile').value;
        if(fileName == ""){
            // Display error message
            document.getElementById('cat6FileUploadErrorMsg').style.display = 'block';
        }else{
            // Get the table reference
            const table = document.getElementById('categorySixTable').getElementsByTagName('tbody')[0];

             // Create a new row
            var newRow = table.insertRow();

            // Insert cells
            var cell1 = newRow.insertCell(0);
            var cell2 = newRow.insertCell(1);
            var cell3 = newRow.insertCell(2);

            // Populate cells with form values
            cell1.innerHTML = table.rows.length; // Auto-increment No column
            cell2.innerHTML = fileName ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';
            cell3.innerHTML = '<span class="btn btn-sm btn-danger" onclick="deleteDBrowPart6('+ insertedId +',1,0)">Delete</span>';

            // Clear form values and hide the form
            document.getElementById('cat6UploadFile').value = '';
            document.getElementById('cat6FileUploadSection').style.display = 'none';
            document.getElementById('cat6UploadLable').style.display = 'none';
            document.getElementById('categorySixAddBtn').style.display = 'none';
            document.getElementById('categorySixClearBtn').style.display = 'none';

        }

    }

    // Category 6 add to DB *******************************
    // Function to delete a row from the table
    function cat6DeleteRow(row,accNo) {
        if(accNo == '1'){
            var rowTemp = document.getElementById("partsix"+row);
            rowTemp.parentNode.removeChild(rowTemp);
        }else{
            categorySixTable.deleteRow(1);
        }

        document.getElementById('cat6form').style.display = 'block';
        document.getElementById('cat6UploadFile').value = '';
        document.getElementById('cat6FileUploadSection').style.display = 'block';
        document.getElementById('cat6UploadLable').style.display = 'block';
        document.getElementById('categorySixAddBtn').style.display = 'block';
        document.getElementById('categorySixClearBtn').style.display = 'block';

        // collapse - enable
        var panelTitleID = 'cat6PanelTitle';
        resetAllPanel(panelTitleID);

    }

    // Delete in the row of database
    function deleteDBrowPart6(deletedId,tblRowId,accNo){
        //   Catagory Six - Ajax request for delete row
        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
            $.ajax({

                url: '/deletePartSixRecord',
                type: 'POST',
                data: {_token: CSRF_TOKEN,
                    deletedId: deletedId,
                },
                dataType: 'JSON',

                success: function (data) {
                    if(data.status){
                        cat6DeleteRow(tblRowId,accNo);
                    }else{
                        alert("Error");
                    }

                },
                error: function(xhr, status, error) {
                    alert("Somethig went worng");
                }

            });
    }
    function AddPartSixRecoredToDb(){
        if(validateCategory06()){
            // Get values from form
            // Get the file input element
            const fileName = document.getElementById('cat6UploadFile').value;
            const panelTitle = document.getElementById('cat6PanelTitle');
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');

            const cat6File1 = document.getElementById('cat6UploadFile');
            var files = $('#cat6UploadFile')[0].files;
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');

            var uploadedData = new FormData();
            uploadedData.append('_token',CSRF_TOKEN);
            uploadedData.append('file',files[0]);

            // Add button disable
            var cat6AddBtn = document.getElementById('categorySixAddBtn');
            // cat6AddBtn.disabled = true;

        //   Catagory Six - Ajax request

        Swal.fire({
            title: "Are you sure that you want to submit?",
            text: "You will not be able to edit the application after submitting it",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes. Submit"
            
        }).then((result) => {
            if (result.isConfirmed) {
                // Ajax start
                    $.ajax({
                    url: '/createPartSixRecord',
                    method: 'post',
                    data: uploadedData,
                    contentType: false,
                    processData: false,
                    dataType: 'json',
                    success: function(data) {
                        if(data.status){
                            cat6AddRow(data.insertedId);
                            // Reset the text color of the panel title
                            panelTitle.style.color = '#021691';

                            // Get elements by class name
                            var toggleLinks = document.getElementsByClassName('toggleLinks');

                            // Loop through the elements and update the data-toggle attribute
                            for (var i = 0; i < toggleLinks.length; i++) {
                                // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                                toggleLinks[i].setAttribute('data-toggle', 'noName');
                            }

                            // Enable the submit and save draft button
                            submitButton.disabled = false;
                            saveDraftButton.disabled = false;


                            //  If successful, Enable the Add button
                            cat6AddBtn.disabled = false;

                            // Redirect to the application.store route
                            window.location.href = "<?php echo e(route('application.store')); ?>";

                        }

                    },
                    error: function(error) {
                        alert("somethig went worng")
                    }
                });
                // Ajax end
                    Swal.fire({
                    title: "Success!",
                    // text: "Your application has been submitted successfuly.",
                    icon: "success"
                });
            }
        });
            
        }

    }



    // Category 6 Validation
    function validateCategory06(){
        // Get the file input element
        const fileName = document.getElementById('cat6UploadFile').value;
        if(fileName == ""){
            // Display error message
            document.getElementById('cat6FileUploadErrorMsg').style.display = 'block';
            return false;
        }else{
            document.getElementById('cat6FileUploadErrorMsg').style.display = 'none';
        }
        return true;
    }

    //Validata upload only .pdf for Category Six
    function validatePDFCategory06() {
        // Get the file input element
        var fileInput = document.getElementById('cat6UploadFile');
        const panelTitle = document.getElementById('cat6PanelTitle');
        const submitButton = document.getElementById('finalSubmitBtn');
        const saveDraftButton = document.getElementById('finalSaveDraftBtn');
        // Get elements by class name
        var toggleLinks = document.getElementsByClassName('toggleLinks');

        // Get the selected file
        var selectedFile = fileInput.files[0];

        // Maximum file size in bytes (35MB)
        var maxSize = 35 * 1024 * 1024;

        // Check if a file is selected
        if (selectedFile) {
            // Get the file extension
            var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

            // Check if the file extension is not 'pdf'
            if (fileExtension !== 'pdf') {
                // Display error message for incorrect file type
                document.getElementById('cat6FileUploadErrorMsg').innerText = '*Please upload a .pdf file.*';
                document.getElementById('cat6FileUploadErrorMsg').style.display = 'block';

                // Reset the text color of the panel title
                panelTitle.style.color = '#021691';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Enable the submit and save draft button
                submitButton.disabled = false;
                saveDraftButton.disabled = false;

                // Clear the file input
                fileInput.value = '';
            } 
            // Check if file size exceeds 35MB
            else if (selectedFile.size > maxSize) {
                // Display error message for file size exceeding 35MB
                document.getElementById('cat6FileUploadErrorMsg').innerText = '*File size should not exceed 35MB.*';
                document.getElementById('cat6FileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            } else {
                // Hide error message
                document.getElementById('cat6FileUploadErrorMsg').style.display = 'none';

                // Change the text color of the panel title
                panelTitle.style.color = '#f2820a';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Disable the submit and save draft button
                submitButton.disabled = true;
                saveDraftButton.disabled = true;
            }
        }
    }

    // Category 6 Clear button
    function cat6Clear(){
            document.getElementById('cat6UploadFile').value = '';
            // Hide error message
             document.getElementById('cat6FileUploadErrorMsg').style.display = 'none';

            //  When it clicks the clear button, Enable the Add button
            var cat6AddBtn = document.getElementById('categorySixAddBtn');
            cat6AddBtn.disabled = false;

            var panelTitleID = 'cat6PanelTitle';
            resetAllPanel(panelTitleID);

        }
</script>

<?php /**PATH D:\Development\research_allowness\resources\views/frontend/application/component/category06.blade.php ENDPATH**/ ?>