@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-file-text-o"></i> Category 1: Indexed Journal Publications
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category1Form" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Indexing Service -->
        <fieldset class="form-group">
          <div class="row">
            <label for="lable" class="col-sm-2 col-form-label">Indexing Service <span class="text-danger">*</span></label>
            <div class="col-sm-10">
              <div class="form-check">
                <input class="form-check-input" type="radio" name="indexingService" id="service1" value="Web of Science" checked>
                <label class="form-check-label" for="service1">
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Web of Science
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="indexingService" id="service2" value="SCOPUS">
                <label class="form-check-label" for="service2">
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SCOPUS
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="indexingService" id="service3" value="Emerald Insight">
                <label class="form-check-label" for="service3">
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Emerald Insight
                </label>
              </div>
            </div>
          </div>
        </fieldset>

        <!-- Title -->
        <div class="form-group row">
          <label for="inputTitle" class="col-sm-2 col-form-label">Title <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="text" name="title" class="form-control" id="inputTitle" placeholder="Title" required>
          </div>
        </div>

        <!-- Journal Name -->
        <div class="form-group row">
          <label for="inputJournalName" class="col-sm-2 col-form-label">Journal Name <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="text" name="journalName" class="form-control" id="inputJournalName" placeholder="Journal Name" required>
          </div>
        </div>

        <!-- Year of Publication -->
        <div class="form-group row" style="margin-bottom: 15px;">
          <label for="inputPublicationYear" class="col-sm-2 col-form-label">Year of Publication <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="number" name="yearOfPublication" class="form-control" id="inputPublicationYear" placeholder="Year">
          </div>
        </div>

        <!-- Publication Volume, Issue and Pages -->
        <div class="form-group row" style="margin-bottom: 5px;">
          <label for="inputPublication" class="col-sm-2 col-form-label">Publication Volume, Issue and Page/s <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="text" name="yearOfPublicationIssue" class="form-control" id="inputPublication" placeholder="Volume (Issue), Page/s">
          </div>
        </div>

        <!-- URL/DOI -->
        <div class="form-group row" style="margin-bottom: 15px;">
          <label for="inputRelevantURL" class="col-sm-2 col-form-label">URL/ DOI of the article <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="url" name="relevantURL" class="form-control" id="inputRelevantURL" placeholder="Relevant URL">
          </div>
        </div>

        <!-- Upload Article File -->
        <div class="form-group row">
          <label for="cat1UploadFile" class="col-sm-2 col-form-label">Upload the relevant full article/ Approved Letter issued by the Research Council <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="file" name="uploadFile" class="form-control" id="cat1UploadFile" accept=".pdf">
          </div>
        </div>

        <!-- Upload Evidence File -->
        <div class="form-group row">
          <label for="cat1EvidenceUploadFile" class="col-sm-2 col-form-label">Upload the evidence for the relevant indexing service/s <span class="text-danger">*</span>
            (<a href="{{ asset('Example/Indexing interface samples.pdf') }}" target="_blank">Sample Evidence File</a>)
          </label>
          <div class="col-sm-10">
            <input type="file" name="uploadEvidenceFile" class="form-control" id="cat1EvidenceUploadFile" accept=".pdf">
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 1
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category1Form').reset();
}
</script>

@endsection
