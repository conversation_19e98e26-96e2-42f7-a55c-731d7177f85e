
<?php $__env->startSection('admin'); ?>
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>


  <style>
    body {
        background-color: #f0f1f2;
    }

    .panel-heading{
        /* background-color: rgb(230, 235, 225); */
        background-color: rgb(255, 255, 255);

    }
    .panel-default h4{
      font-weight: bold;
      color: rgb(19, 8, 180);
    }
    .panel-default a{
      text-decoration: none;
    }
    #inputGrantDetails{
        resize: vertical;
    }
    #inputErrorMsg{
        color: red;
    }
    .allInputErrorMsg, #finalBtnDisabledErrorMsg{
        color: red;
        font-style: italic;
        font-size: 12px;
    }
    .custInput {
        background-color: transparent;
        border: 0px solid;
        cursor: default;
    }
    .userDetails{
        height: 200px;
    }
    .page-description{
        margin-top: 10px;
    }
    .page-description p{
        font-weight: bold;
    }
    .add-btn{
        font-weight: bold;
    }
    .clear-btn{
        width: 70px;
        margin-left: -8px;
        /* color: black; */
        font-weight: bold;
    }
    /* Add your custom styles here */
    .custom-panel-heading {
            background-color: #f0f1f2; /* background color */
            color: #fff; /*  text color */
            cursor: pointer;
        }
    .text-primary{
        color: #0B1F33;
    }
    .panel-heading .panel-title{
        color: #1F7DDB;
    }
    /* Submit and Save Draft button */
    .submitBtn{
        border-color: white;
        color: white;
        background-color: #1F7DDB;
        width: 95px;
        font-weight: bold;
    }
    .submitBtn:hover{
        color: white;
        font-weight: bold;
        background-color: #1964B0;
        border-color: #1964B0;
    }
    .draftBtn{
        border-color: white;
        color: white;
        background-color: #535B63;
        /* width: 95px; */
        font-weight: bold;
    }
    .draftBtn:hover{
        color: white;
        font-weight: bold;
        background-color: #535B8D;
        border-color: #535B8D;
    }

    .container1 {
        margin-left: 10px;
        margin-right: 10px;
        padding: 10px;
    }
    /* required asterisk*/
    .asterisk{
        color: red;
    }
    .userDetailsView label{
        color: #755A5A;
    }
    .hasAFile{
        color: green;
        font-weight: bold;
    }
    .noFile{
        color: red;
        font-weight: bold;
    }
    .badge-success{
        background-color: green;
    }
    /* User details */
    .text-dark{
        color: #A82727;
    }
  </style>

  <?php
   $currentYear =  date('Y');
  ?>
  <section class="content">

    <div class="container1" style="background-color: white;">
        <!-- Site header details -->
        <h2 style="color: #A82727; font-weight: bold;">Application for Research Allowance - <?php echo e(date('Y')); ?></h2>
        <h4 style="color: #A05A5A;">University of Sri Jayewardenepura</h4>
    </div>
    <!-- Personal Information -->
    <?php echo $__env->make('frontend.application.component.personal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <div class="container1 page-description">
        <!-- white space -->
    </div>

<div class="container1" style="background-color: white;">
  <!-- <p><strong>Note:</strong> A <strong> System</strong> under development</p> -->
<?php if(!isset($appliedCat_number)): ?>
    <input type="hidden" value="0" id="CatNO">
<?php else: ?>
    <input type="hidden" value="<?php echo e($appliedCat_number); ?>" id="CatNO">
<?php endif; ?>

  <?php if($researchProposalCount == 0): ?>
  <div class="page-description">
    <p>Choose the category you desire to apply and furnish the requested data.</p>
  </div>
  <?php endif; ?>
  <div style="padding: 3px;">

  </div>
  <div class="panel-group" id="accordion">
    <?php if($cat1_previous_application_status > 0): ?>
    <!-- Category one -->
    <?php echo $__env->make('frontend.application.component.category01a', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php elseif($researchProposalCount == 0 || $finalAccept21_22 == 1 || $finalAccept2023 == 1): ?>
        <?php if($countOfCat7App % 2 === 1): ?>
            <!-- Category seven Final report-->
            <?php echo $__env->make('frontend.application.component.category07a', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php else: ?>
            <!-- Category one -->
            <?php echo $__env->make('frontend.application.component.category01', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Category two -->
            <?php echo $__env->make('frontend.application.component.category02', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Category Three -->
            <?php echo $__env->make('frontend.application.component.category03', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Category four -->
            <?php echo $__env->make('frontend.application.component.category04', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Category five -->
            <?php echo $__env->make('frontend.application.component.category05', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Category six -->
            <?php echo $__env->make('frontend.application.component.category06', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Category seven -->
            <?php echo $__env->make('frontend.application.component.category07', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Category eight -->
            <?php echo $__env->make('frontend.application.component.category08', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>
    <?php elseif($researchProposalCount == 1): ?>
    <!-- Category seven -->
    <?php echo $__env->make('frontend.application.component.category07a', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <br>
    <!-- From Submit Section -->
        <!-- *******************currently not visible****************** -->
        <form method="post" action="<?php echo e(route('application.store')); ?>" style="display: none;">
        <?php echo csrf_field(); ?>


            <div class="form-group row" style="margin-top: 10px;">
                <!-- Additional Information submit button -->
                <div class="col col-sm-6 col-md-1" style="margin-right: 5px;">
                    <input type="submit" name="submit" id="finalSubmitBtn" class="btn btn-md submitBtn" value="Submit" />
                </div>
                <!-- Additional Information draft button -->
                <div class="col col-sm-6 col-md-2">
                    <input type="submit" name="saveDraft" id="finalSaveDraftBtn" class="btn btn-md draftBtn" value="Save as Draft"/>
                </div>
            </div>
            <p id="finalBtnDisabledErrorMsg" style="display: none;">*The data you started to fill has not been completed.
        Please click the add button to proceed or clear the entered data*</p>
        </form>
        <!-- *******************form end******************** -->

    <!-- get Emp no for the special reason of category 7 open -->
    <input type="hidden" value="<?php echo e(auth()->user()->empNo); ?>" id="permittedEmpNo">
  </div>
</div>
  </section>

    <script>

        //All Category clear functions
        function resetAllPanel(titleID){
            const panelTitle = document.getElementById(titleID);
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');
            // Get elements by class name
            var toggleLinks = document.getElementsByClassName('toggleLinks');

            // Reset the text color of the panel title
            panelTitle.style.color = '#1F7DDB';

            // Loop through the elements and update the data-toggle attribute
            for (var i = 0; i < toggleLinks.length; i++) {
                toggleLinks[i].setAttribute('data-toggle', 'collapse');
            }

            // Enable the submit and save draft button
            submitButton.disabled = false;
            saveDraftButton.disabled = false;
        }


        // Add an event listener to the toggleLinks class
        document.addEventListener('DOMContentLoaded', function () {
            const toggleLinks = document.querySelectorAll('.toggleLinks');

            toggleLinks.forEach(function (link) {
                link.addEventListener('click', function () {
                    // Remove the custom-panel-heading class from all panel headings
                    const allPanelHeadings = document.querySelectorAll('.panel-heading');
                    allPanelHeadings.forEach(function (panelHeading) {
                        panelHeading.classList.remove('custom-panel-heading');
                    });

                    // Find the closest panel-heading and toggle the custom class
                    const panelHeading = this.querySelector('.panel-heading');
                    panelHeading.classList.add('custom-panel-heading');
                });
            });
        });

        // Allowing only the applied panel to open on restart
        document.addEventListener('DOMContentLoaded', function () {
            // Get elements by class name
            var toggleLinks = document.getElementsByClassName('toggleLinks');

            var CategoryNo = document.getElementById('CatNO').value;
            // Loop through the elements and update the data-toggle attribute
            // alert(CategoryNo);
            for (var i = 0; i < toggleLinks.length; i++) {

                if(CategoryNo != 0){
                    if(CategoryNo == i+1){
                        toggleLinks[i].setAttribute('data-toggle', 'collapse');
                    }else
                    {
                        toggleLinks[i].setAttribute('data-toggle', 'noName');
                    }
                }

            }

        });

        // check everytime final sunmit and save draft buttons are disabled or not
        document.addEventListener('DOMContentLoaded', function() {
        // Run this function every second
        setInterval(function() {
            console.log('This code runs every second');
            // Add your code logic here
                const submitButton = document.getElementById('finalSubmitBtn');
                const saveDraftButton = document.getElementById('finalSaveDraftBtn');

                if (submitButton.disabled) {
                    document.getElementById('finalBtnDisabledErrorMsg').style.display = "block";
                }else{
                    document.getElementById('finalBtnDisabledErrorMsg').style.display = "none";
                }

                // Category 7 proposal cannot be submitted after April 1*****************************
                const date = new Date();

                let day = date.getDate();
                let month = date.getMonth() + 1;
                let year = date.getFullYear();

                // This arrangement can be altered based on how we want the date's format to appear.
                // let currentDate = `${day}-${month}-${year}`;
                // alert(currentDate);
                if(month >= 4){  //4
                    // Get elements by class name
                    var toggleLinks = document.getElementsByClassName('toggleLinks');
                    toggleLinks[6].setAttribute('data-toggle', 'noName');
                    var permittedEmpNo = document.getElementById('permittedEmpNo').value;

                    // 7204, 9292, 12075, 9705
                    if(permittedEmpNo == 0 ){
                        var toggleLinks = document.getElementsByClassName('toggleLinks');
                        toggleLinks[6].setAttribute('data-toggle', 'collapse');
                    }
                }
                // ************************************************************************************

            }, 1000); // 1000 milliseconds = 1 second
        });

    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\research_allowness\resources\views/frontend/application/index.blade.php ENDPATH**/ ?>