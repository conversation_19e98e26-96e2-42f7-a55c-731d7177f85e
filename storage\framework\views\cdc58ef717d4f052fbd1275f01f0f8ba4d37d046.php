<?php $__env->startSection('admin'); ?>

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-cogs"></i> Category 8: Innovation and Invention
      </h3>
      <div class="card-tools">
        <a href="<?php echo e(route('application.category.list')); ?>" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category8Form" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <!-- Upload Invention Disclosure Form -->
        <fieldset class="form-group">
          <div class="row">
            <label for="lable" class="col-sm-10 col-form-label">Upload the Invention Disclosure Form approved by the University Business Linkage Cell <span class="text-danger">*</span></label>
          </div>
        </fieldset>
        <div class="form-group row">
          <div class="col-sm-10">
            <input type="file" name="cat8UploadFile1" class="form-control" id="cat8InventionFile" accept=".pdf">
          </div>
        </div>

        <!-- Upload Evidence -->
        <fieldset class="form-group">
          <div class="row">
            <label for="lable" class="col-sm-10 col-form-label">Upload the Evidence to show the product in the market or service being used in commercially</label>
          </div>
        </fieldset>
        <div class="form-group row">
          <div class="col-sm-10">
            <input type="file" name="cat8UploadFile2" class="form-control" id="cat8EvidenceFile" accept=".pdf">
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 8
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category8Form').reset();
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\research_allowness\resources\views/frontend/application_new/component/category8.blade.php ENDPATH**/ ?>