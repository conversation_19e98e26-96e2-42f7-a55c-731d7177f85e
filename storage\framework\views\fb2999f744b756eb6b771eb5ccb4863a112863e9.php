<?php $__env->startSection('admin'); ?>

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-graduation-cap"></i> Category 4: Other Scholarly Work
      </h3>
      <div class="card-tools">
        <a href="<?php echo e(route('application.category.list')); ?>" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category4Form" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <!-- Instructions -->
        <p>Have you <strong>completed/ produced/ published</strong> any of the following academic work during the year <?php echo e(date('Y')); ?>?</p>

        <!-- Academic Work -->
        <div class="form-group row">
          <label for="academicWorkSelect" class="col-sm-2 col-form-label">Academic Work <span class="text-danger">*</span></label>
          <div class="col-sm-8">
            <select name="academicWork" class="form-control" id="academicWorkSelect" required>
              <option style="background-color: gray; color: white;" value="Academic Work" selected>Academic Work</option>
              <option value="Review Article">Review Article</option>
              <option value="Editorial">Editorial</option>
              <option value="Case Report">Case Report</option>
              <option value="Letter to the Editor">Letter to the Editor</option>
              <option value="Debating Document">Debating Document</option>
              <option value="Text Book">Text Book</option>
              <option value="Research Thesis">Research Thesis</option>
              <option value="Book/ Book Chapter">Book/ Book Chapter</option>
              <option value="Any other Creative Work(Ex: Drama, Music etc)">Any Other Creative Work (Ex: Drama, Music etc.)</option>
            </select>
          </div>
        </div>

        <!-- Date and Details -->
        <div class="form-group row">
          <label for="inputCategory4Date" class="col-sm-2 col-form-label">Date of completed/ produced/ published <span class="text-danger">*</span></label>
          <div class="col-sm-2">
            <input name="publicationDate" type="date" class="form-control" id="inputCategory4Date" required>
          </div>
          <div class="col-sm-6">
            <input name="details" type="text" class="form-control" id="inputCategory4Details" placeholder="Remarks" required>
          </div>
        </div>

        <!-- Upload File -->
        <div class="form-group row">
          <label for="cat4UploadFile" class="col-sm-2 col-form-label">Upload the proof document <span class="text-danger">*</span></label>
          <div class="col-sm-8">
            <input type="file" name="uploadFile" class="form-control" id="cat4UploadFile" accept=".pdf">
          </div>
        </div>

        <!-- Additional Details -->
        <div class="form-group row">
          <label for="additionalDetails" class="col-sm-3 col-form-label">
            Additional Details <small>(Optional)</small>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="additionalDetails" name="additional_details" rows="3"
                      placeholder="Provide any additional relevant details"></textarea>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 4
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category4Form').reset();
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\research_allowness\resources\views/frontend/application_new/component/category4.blade.php ENDPATH**/ ?>