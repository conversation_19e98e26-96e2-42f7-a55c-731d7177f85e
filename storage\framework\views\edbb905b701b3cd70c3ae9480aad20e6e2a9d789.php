<?php $__env->startSection('admin'); ?>

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-newspaper-o"></i> Category 2: Other Publications
      </h3>
      <div class="card-tools">
        <a href="<?php echo e(route('application.category.list')); ?>" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category2Form" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <!-- Instructions -->
        <div class="alert alert-info">
          <h5><i class="icon fa fa-info"></i> Instructions</h5>
          Please provide details of your other publications (non-indexed journals, conference papers, book chapters, etc.).
        </div>

        <!-- Publication Type -->
        <div class="form-group row">
          <label for="publicationType" class="col-sm-3 col-form-label">
            Publication Type <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="publicationType" name="publication_type" required>
              <option value="">Select Publication Type</option>
              <option value="Conference Paper">Conference Paper</option>
              <option value="Book Chapter">Book Chapter</option>
              <option value="Book">Book</option>
              <option value="Non-Indexed Journal">Non-Indexed Journal Article</option>
              <option value="Technical Report">Technical Report</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>

        <!-- Publication Title -->
        <div class="form-group row">
          <label for="publicationTitle" class="col-sm-3 col-form-label">
            Publication Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="publicationTitle" name="publication_title"
                   placeholder="Enter the title of your publication" required>
          </div>
        </div>

        <!-- Journal/Conference/Book Name -->
        <div class="form-group row">
          <label for="venueName" class="col-sm-3 col-form-label">
            Journal/Conference/Book Name <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="venueName" name="venue_name"
                   placeholder="Enter the journal, conference, or book name" required>
          </div>
        </div>

        <!-- Authors -->
        <div class="form-group row">
          <label for="authors" class="col-sm-3 col-form-label">
            Authors <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="authors" name="authors" rows="2"
                      placeholder="List all authors (including yourself)" required></textarea>
          </div>
        </div>

        <!-- Publication Details -->
        <div class="form-group row">
          <label for="publicationDetails" class="col-sm-3 col-form-label">
            Publication Details <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="publicationDetails" name="publication_details"
                   placeholder="e.g., Vol. 15(2), pp. 123-135 or ISBN: 978-0123456789" required>
          </div>
        </div>

        <!-- Year of Publication -->
        <div class="form-group row">
          <label for="yearOfPublication" class="col-sm-3 col-form-label">
            Year of Publication <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="number" class="form-control" id="yearOfPublication" name="year_of_publication"
                   min="2020" max="<?php echo e(date('Y')); ?>" value="<?php echo e(date('Y')); ?>" required>
          </div>
        </div>

        <!-- Publication File Upload -->
        <div class="form-group row">
          <label for="publicationFile" class="col-sm-3 col-form-label">
            Publication File <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="publicationFile" name="publication_file"
                   accept=".pdf,.doc,.docx" required>
            <small class="form-text text-muted">Upload PDF, DOC, or DOCX file (Max: 10MB)</small>
          </div>
        </div>

        <!-- URL (Optional) -->
        <div class="form-group row">
          <label for="publicationUrl" class="col-sm-3 col-form-label">
            Publication URL <small>(Optional)</small>
          </label>
          <div class="col-sm-9">
            <input type="url" class="form-control" id="publicationUrl" name="publication_url"
                   placeholder="https://example.com/publication">
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="button" class="btn btn-info mr-2" onclick="saveDraft()">
              <i class="fa fa-save"></i> Save as Draft
            </button>
            <button type="submit" class="btn btn-success">
              <i class="fa fa-plus"></i> Add Publication
            </button>
          </div>
        </div>
      </form>

      <!-- Publications List -->
      <div class="mt-4">
        <h5>Added Other Publications</h5>
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead class="thead-dark">
              <tr>
                <th>No.</th>
                <th>Type</th>
                <th>Title</th>
                <th>Venue</th>
                <th>Details</th>
                <th>Year</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="otherPublicationsTableBody">
              <tr>
                <td colspan="7" class="text-center text-muted">No other publications added yet</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category2Form').reset();
}

function saveDraft() {
    alert('Draft saved successfully!');
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\research_allowness\resources\views/frontend/application_new/component/category2.blade.php ENDPATH**/ ?>