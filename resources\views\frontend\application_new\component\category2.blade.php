@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-newspaper-o"></i> Category 2: Other Publications
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category2Form" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Publication Type -->
        <fieldset class="form-group">
          <div class="row">
            <label for="lable" class="col-sm-2 col-form-label">Publication <span class="text-danger">*</span></label>
            <div class="col-sm-10">
              <div class="form-check">
                <input class="form-check-input" type="radio" name="publication" id="cat2GridRadios1" value="non-Indexed Journal Publication" checked>
                <label class="form-check-label" for="cat2GridRadios1">
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Non-Indexed Journal Publication
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="publication" id="cat2GridRadios4" value="Cabell's International">
                <label class="form-check-label" for="cat2GridRadios4">
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Cabell's International
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="publication" id="cat2GridRadios2" value="Symposium/ Conference/ Technical Session Paper, Abstract or Poster">
                <label class="form-check-label" for="cat2GridRadios2">
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Symposium/ Conference/ Technical Session Paper/ Abstract or Poster
                </label>
              </div>
            </div>
          </div>
        </fieldset>

        <!-- Title -->
        <div class="form-group row">
          <label for="cat2InputTitle" class="col-sm-2 col-form-label">Title <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="text" name="title" class="form-control" id="cat2InputTitle" placeholder="Title" required>
          </div>
        </div>

        <!-- Journal/Conference Name -->
        <div class="form-group row">
          <label for="cat2InputJournalName" class="col-sm-2 col-form-label">Name of the Journal/ Symposium/ Conference/ Technical Session <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="text" name="sessionName" class="form-control" id="cat2InputJournalName" placeholder="Journal/ Symposium/ Conference/ Technical Session Name" required>
          </div>
        </div>

        <!-- Status -->
        <div class="form-group row">
          <label for="checkPublication" class="col-sm-2 col-form-label">Status <span class="text-danger">*</span></label>
          <div class="col-sm-2">
            <div class="form-check">
              <input class="form-check-input" type="radio" name="cat2Status" id="cat2CheckAccepted2" value="1">
              <label class="form-check-label" for="cat2CheckAccepted2">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Accepted
              </label>
            </div>
          </div>
          <div class="col-sm-2">
            <div class="form-check">
              <input class="form-check-input" type="radio" name="cat2Status" id="cat2CheckPublication2" value="2">
              <label class="form-check-label" for="cat2CheckPublication2">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Published
              </label>
            </div>
          </div>
        </div>

        <!-- Date of Acceptance -->
        <div class="form-group row" style="margin-bottom: 0px;">
          <label for="cat2InputAcceptDate2" class="col-sm-2 col-form-label"></label>
          <label for="cat2InputAcceptDate2" class="col-sm-2 col-form-label">Date of Acceptance <span class="text-danger">*</span></label>
          <div class="col-sm-8">
            <input type="date" name="dateOfAcceptance" class="form-control" id="cat2InputAcceptDate2">
          </div>
        </div>

        <!-- Upload File -->
        <div class="form-group row">
          <label for="cat2UploadFile" class="col-sm-2 col-form-label">Upload the relevant full article/ Approved Letter issued by the Research Council <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="file" name="uploadFile" class="form-control" id="cat2UploadFile" accept=".pdf">
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 2
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category2Form').reset();
}
</script>

@endsection
