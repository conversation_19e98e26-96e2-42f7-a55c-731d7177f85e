<?php $__env->startSection('admin'); ?>

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-university"></i> Category 6: Postgraduate Studies
      </h3>
      <div class="card-tools">
        <a href="<?php echo e(route('application.category.list')); ?>" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category6Form" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <!-- Instructions -->
        <fieldset class="form-group">
          <div class="row">
            <label for="lable" class="col-sm-10 col-form-label">Upload a confirmation letter from Faculty of Graduate Studies/respective authority if you have registered for a postgraduate degree with a research component in the current year <span class="text-danger">*</span></label>
          </div>
        </fieldset>

        <!-- Upload File -->
        <div class="form-group row">
          <div class="col-sm-10">
            <input type="file" name="cat6UploadFile" class="form-control" id="cat6UploadFile" required accept=".pdf">
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 6
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category6Form').reset();
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\research_allowness\resources\views/frontend/application_new/component/category6.blade.php ENDPATH**/ ?>