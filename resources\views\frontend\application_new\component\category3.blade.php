@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-money"></i> Category 3: Research Grants
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category3Form" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Instructions -->
        <div class="alert alert-info">
          <h5><i class="icon fa fa-info"></i> Instructions</h5>
          Please provide details of research grants received from international or national organizations.
        </div>

        <!-- Grant Type -->
        <div class="form-group row">
          <label for="grantType" class="col-sm-3 col-form-label">
            Grant Type <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="grantType" name="grant_type" required>
              <option value="">Select Grant Type</option>
              <option value="International">International Grant</option>
              <option value="National">National Grant</option>
              <option value="University">University Grant</option>
              <option value="Industry">Industry Grant</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>

        <!-- Grant Title -->
        <div class="form-group row">
          <label for="grantTitle" class="col-sm-3 col-form-label">
            Grant/Project Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="grantTitle" name="grant_title"
                   placeholder="Enter the title of your research grant/project" required>
          </div>
        </div>

        <!-- Funding Organization -->
        <div class="form-group row">
          <label for="fundingOrganization" class="col-sm-3 col-form-label">
            Funding Organization <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="fundingOrganization" name="funding_organization"
                   placeholder="Enter the name of the funding organization" required>
          </div>
        </div>

        <!-- Grant Amount -->
        <div class="form-group row">
          <label for="grantAmount" class="col-sm-3 col-form-label">
            Grant Amount <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <div class="input-group">
              <div class="input-group-prepend">
                <select class="form-control" name="currency" required>
                  <option value="LKR">LKR</option>
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                </select>
              </div>
              <input type="number" class="form-control" id="grantAmount" name="grant_amount"
                     placeholder="Enter grant amount" step="0.01" required>
            </div>
          </div>
        </div>

        <!-- Grant Period -->
        <div class="form-group row">
          <label for="grantPeriod" class="col-sm-3 col-form-label">
            Grant Period <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <div class="row">
              <div class="col-sm-6">
                <input type="date" class="form-control" id="startDate" name="start_date"
                       placeholder="Start Date" required>
                <small class="form-text text-muted">Start Date</small>
              </div>
              <div class="col-sm-6">
                <input type="date" class="form-control" id="endDate" name="end_date"
                       placeholder="End Date" required>
                <small class="form-text text-muted">End Date</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Role in Project -->
        <div class="form-group row">
          <label for="roleInProject" class="col-sm-3 col-form-label">
            Your Role <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="roleInProject" name="role_in_project" required>
              <option value="">Select Your Role</option>
              <option value="Principal Investigator">Principal Investigator</option>
              <option value="Co-Principal Investigator">Co-Principal Investigator</option>
              <option value="Co-Investigator">Co-Investigator</option>
              <option value="Research Associate">Research Associate</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>

        <!-- Grant Document Upload -->
        <div class="form-group row">
          <label for="grantDocument" class="col-sm-3 col-form-label">
            Grant Document <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="grantDocument" name="grant_document"
                   accept=".pdf,.doc,.docx" required>
            <small class="form-text text-muted">Upload grant award letter or agreement (PDF, DOC, DOCX) - Max: 10MB</small>
          </div>
        </div>

        <!-- Project Description -->
        <div class="form-group row">
          <label for="projectDescription" class="col-sm-3 col-form-label">
            Project Description <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="projectDescription" name="project_description" rows="4"
                      placeholder="Provide a brief description of the research project" required></textarea>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 3
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category3Form').reset();
}
</script>

@endsection
