<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Application\IndexController as ApplicationIndexController;
use App\Http\Controllers\Backend\ApplicationProcessController;
use App\Http\Controllers\Backend\CoChairController;
use App\Http\Controllers\Backend\FinalController;
use App\Http\Controllers\Backend\LibrarianController;
use App\Http\Controllers\Backend\UserController;
use App\Http\Controllers\Backend\VcController;
use App\Http\Controllers\Frontend\IndexController;
use App\Http\Controllers\ManageController;
use App\Http\Controllers\PartEightController;
use App\Http\Controllers\PartFiveController;
use App\Http\Controllers\PartFourController;
use App\Http\Controllers\PartOneController;
use App\Http\Controllers\PartSevenController;
use App\Http\Controllers\PartSixController;
use App\Http\Controllers\PartThreeController;
use App\Http\Controllers\PartTwoController;
use App\Http\Controllers\SSO\SSOController;
use App\Models\PartOne;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

    Route::get("/sso/login", [SSOController::class, 'getLogin'])->name("sso.login");
    Route::get("/callback", [SSOController::class, 'getCallback'])->name("sso.callback");
    Route::get("/sso/connect", [SSOController::class, 'connectUser'])->name("sso.connect");
    Route::get("/sso/availability", [SSOController::class, 'userAvailerbility']);

    Route::get('/', [AdminController::class,'index'])->middleware(['auth'])->name('dashboard');

    Route::get('/details/instruction', [IndexController::class, "showInstruction"])->name('details.instruction');

Route::middleware('auth','role:admin|sc|ra|cochair')->prefix('profile')->group(function(){

	Route::get('/view',[AdminController::class, 'ProfileView'])->name('profile.view');

});//user profile controller route list

Route::middleware('auth','role:admin')->prefix('user')->group(function(){

	Route::get('/view',[UserController::class, 'UserView'])->name('user.view');
    Route::get('/add/view',[UserController::class, 'UserAddView'])->name('user.add.view');
    Route::post('/store',[UserController::class, 'UserStore'])->name('user.store');
    Route::get('/edit/{id}',[UserController::class, 'UserEdit'])->name('user.edit');
    Route::post('/update/{id}',[UserController::class, 'UserUpdate'])->name('user.update');
    Route::get('/delete/{id}',[UserController::class, 'UserDelete'])->name('user.delete');
    Route::get('/inactive/{id}', [UserController::class, 'UserInactive'])->name('user.inactive');
    Route::get('/active/{id}', [UserController::class, 'UserActive'])->name('user.active');
    Route::get('/show/{id}',[UserController::class, 'UserShow'])->name('user.show');
    Route::post('/users/{user}/roles', [UserController::class, 'assignRole'])->name('users.roles');
    Route::get('/users/{user}/roles/{role}', [UserController::class, 'removeRole'])->name('users.roles.remove');
    Route::post('/users/{user}/permissions', [UserController::class, 'givePermission'])->name('users.permissions');
    Route::get('/users/{user}/permissions/{permission}', [UserController::class, 'revokePermission'])->name('users.permissions.revoke');

});//user manage controller route list


Route::middleware('auth','role:admin')->prefix('role')->group(function () {

    Route::get('/index', [RoleController::class, 'roleIndex'])->name('role.index');
    Route::get('/add', [RoleController::class, 'roleAdd'])->name('role.add');
    Route::post('/store', [RoleController::class, 'roleStore'])->name('role.store');
    Route::get('/edit/{id}', [RoleController::class, 'roleEdit'])->name('role.edit');
    Route::post('/update/{id}', [RoleController::class, 'roleUpdate'])->name('role.update');
    Route::get('/delete/{id}', [RoleController::class, 'roleDelete'])->name('role.delete');
    Route::post('permission/role/update/{role}', [RoleController::class, 'permissionRoleUpdate'])->name('role.permission.update');
    Route::get('/roles/{role}/permissions/{permission}', [RoleController::class, 'revokePermission'])->name('roles.permissions.revoke');

});//system role management route list

Route::middleware('auth','role:admin')->prefix('permission')->group(function () {
    Route::get('/index', [PermissionController::class, 'permissionIndex'])->name('permission.index');
    Route::get('/add', [PermissionController::class, 'permissionAdd'])->name('permission.add');
    Route::post('/store', [PermissionController::class, 'permissionStore'])->name('permission.store');
    Route::get('/edit/{id}', [PermissionController::class, 'permissionEdit'])->name('permission.edit');
    Route::post('/update/{id}', [PermissionController::class, 'permissionUpdate'])->name('permission.update');
    Route::get('/delete/{id}', [PermissionController::class, 'permissionDelete'])->name('permission.delete');
    Route::post('/permissions/{permission}/roles', [PermissionController::class, 'assignRole'])->name('permissions.roles');
    Route::get('/permissions/{permission}/roles/{role}', [PermissionController::class, 'removeRole'])->name('permissions.roles.remove');

});//system permission route list

Route::middleware('auth','role:applicant')->group(function () {

    Route::get('/viewApplication', [ManageController::class, "manageApplicationView"])->name('researchAllowanceList');
    Route::post('/create/application',[ManageController::class , "createNewApplication"])->name("careateNewApplication");
    Route::get('/new/application', [ManageController::class, 'OpenNewApplication'])->name('openNewApplication');
    Route::get('/application/draft/{refNo}', [ManageController::class, "manageDraftApplicationView"])->name("manageDraftApplicationView");
    Route::get('/application/final', [ManageController::class, 'manageSubmittedApplicationView'])->name('application.store');

    Route::get('/submittedApp/history', [ManageController::class, "submittedAppHistory"])->name('submittedAppHistory');
    Route::get('/submittedApp/{refNo}', [ManageController::class, "submittedSummery"])->name('submittedSummery');

    Route::get('/cat1/pdf/show/{id}', [ManageController::class, 'Cat1Pdf'])->name('cat1.pdf.show');
    Route::get('/cat1a/pdf/show/{id}', [ManageController::class, 'Cat1aPdf'])->name('cat1a.pdf.show');
    Route::get('/cat2/pdf/show/{id}', [ManageController::class, 'Cat2Pdf'])->name('cat2.pdf.show');
    Route::get('/cat3/pdf/show/{id}', [ManageController::class, 'Cat3Pdf'])->name('cat3.pdf.show');
    Route::get('/cat4/pdf/show/{id}', [ManageController::class, 'Cat4Pdf'])->name('cat4.pdf.show');
    Route::get('/cat5/pdf/show/{id}', [ManageController::class, 'Cat5Pdf'])->name('cat5.pdf.show');
    Route::get('/cat6/pdf/show/{id}', [ManageController::class, 'Cat6Pdf'])->name('cat6.pdf.show');
    Route::get('/cat7/pdf/show/{id}', [ManageController::class, 'Cat7Pdf'])->name('cat7.pdf.show');
    Route::get('/cat7a/pdf/show/{id}', [ManageController::class, 'Cat7aPdf'])->name('cat7a.pdf.show');
    Route::get('/cat8/pdf/show/{id}', [ManageController::class, 'Cat8Pdf'])->name('cat8.pdf.show');
    Route::get('/cat8a/pdf/show/{id}', [ManageController::class, 'Cat8aPdf'])->name('cat8a.pdf.show');


    // Part one or Category one route
    Route::post('/createPartOneRecord', [PartOneController::class, 'store']);
    Route::post('/deletePartOneRecord', [PartOneController::class, 'delete']);

    // Part two or Category two route
    Route::post('/createPartTwoRecord', [PartTwoController::class, 'store']);
    Route::post('/deletePartTwoRecord', [PartTwoController::class, 'delete']);

    // Part three or Category three route
    Route::post('/createPartThreeRecord', [PartThreeController::class, 'store']);
    Route::post('/deletePartThreeRecord', [PartThreeController::class, 'delete']);
    Route::post('/updatePartThreeRecord', [PartThreeController::class, 'update']);

    // Part four or Category four route
    Route::post('/createPartFourRecord', [PartFourController::class, 'store']);
    Route::post('/deletePartFourRecord', [PartFourController::class, 'delete']);

    // Part five or Category five route
    Route::post('/createPartFiveRecord', [PartFiveController::class, 'store']);
    Route::post('/deletePartFiveRecord', [PartFiveController::class, 'delete']);

    // Part six or Category six route
    Route::post('/createPartSixRecord', [PartSixController::class, 'store']);
    Route::post('/deletePartSixRecord', [PartSixController::class, 'delete']);

    // Part seven or Category seven route
    Route::post('/createPartSevenRecord', [PartSevenController::class, 'store']);
    Route::post('/deletePartSevenRecord', [PartSevenController::class, 'delete']);

    // Part seven A or Category seven A route
    Route::post('/createPartSevenARecord', [PartSevenController::class, 'storeA']);
    Route::post('/deletePartSevenARecord', [PartSevenController::class, 'deleteA']);

    // Part Eight or Category Eight route
    Route::post('/createPartEightRecord', [PartEightController::class, 'store']);
    Route::post('/deletePartEightRecord', [PartEightController::class, 'delete']);

    // Save again category 1
    Route::post('/saveAgainCat1', [PartOneController::class, 'saveAgain_Cat1Table'])->name('saveAgain_Cat1Table.store');


    //New application routes
    Route::get('/new/application', [ApplicationIndexController::class, 'index'])->name('new.application.index');

     Route::get('/application/category/list', [ApplicationIndexController::class, 'ApplicationCategoryList'])->name('application.category.list');

});

Route::middleware('auth','role:applicant')->prefix('apply')->group(function () {

});

Route::middleware('auth','role:admin|sc|ra')->prefix('application')->group(function () {

    Route::get('/index', [ApplicationProcessController::class, 'AppShow'])->name('app.show');
    Route::match(['get', 'post'], '/summary', [ApplicationProcessController::class, 'appSummary'])->name('app.summary');
    Route::get('/application/ma/view/{refNo}', [ApplicationProcessController::class, "AppMaView"])->name('app.ma.view');
    Route::get('/application/status/view/{refNo}', [ApplicationProcessController::class, "AppStatusView"])->name('app.status.view');

    Route::get('/cat1/pdf/{id}', [ApplicationProcessController::class, 'Cat1Pdf'])->name('cat1.pdf');
    Route::get('/cat1a/pdf/{id}', [ApplicationProcessController::class, 'Cat1aPdf'])->name('cat1a.pdf');
    Route::get('/cat2/pdf/{id}', [ApplicationProcessController::class, 'Cat2Pdf'])->name('cat2.pdf');
    Route::get('/cat3/pdf/{id}', [ApplicationProcessController::class, 'Cat3Pdf'])->name('cat3.pdf');
    Route::get('/cat4/pdf/{id}', [ApplicationProcessController::class, 'Cat4Pdf'])->name('cat4.pdf');
    Route::get('/cat5/pdf/{id}', [ApplicationProcessController::class, 'Cat5Pdf'])->name('cat5.pdf');
    Route::get('/cat6/pdf/{id}', [ApplicationProcessController::class, 'Cat6Pdf'])->name('cat6.pdf');
    Route::get('/cat7/pdf/{id}', [ApplicationProcessController::class, 'Cat7Pdf'])->name('cat7.pdf');
    Route::get('/cat7a/pdf/{id}', [ApplicationProcessController::class, 'Cat7aPdf'])->name('cat7a.pdf');
    Route::get('/cat8/pdf/{id}', [ApplicationProcessController::class, 'Cat8Pdf'])->name('cat8.pdf');
    Route::get('/cat8a/pdf/{id}', [ApplicationProcessController::class, 'Cat8aPdf'])->name('cat8a.pdf');

    Route::post('/application/ma/forward', [ApplicationProcessController::class, 'AppMaForword'])->name('app.ma.forward');

    Route::get('/cat7/proposal/list', [ApplicationProcessController::class, 'cat7ProposalList'])->name('cat7.proposal.list');

    Route::get('/cat7/summary/list', [ApplicationProcessController::class, 'cat7SummaryList'])->name('cat7.summary.list');

});//system permission route list

Route::middleware('auth','role:admin|cochair')->prefix('cochair')->group(function () {

    Route::get('/index', [CoChairController::class, 'AppView'])->name('app.cochair.view');
    Route::get('/application/view/{refNo}', [CoChairController::class, "AppCoChairShow"])->name('app.cochair.show');

    Route::get('/cat1/pdf/{id}', [CoChairController::class, 'Cat1Pdf'])->name('cat1cochair.pdf');
    Route::get('/cat1a/pdf/{id}', [CoChairController::class, 'Cat1aPdf'])->name('cat1acochair.pdf');
    Route::get('/cat2/pdf/{id}', [CoChairController::class, 'Cat2Pdf'])->name('cat2cochair.pdf');
    Route::get('/cat3/pdf/{id}', [CoChairController::class, 'Cat3Pdf'])->name('cat3cochair.pdf');
    Route::get('/cat4/pdf/{id}', [CoChairController::class, 'Cat4Pdf'])->name('cat4cochair.pdf');
    Route::get('/cat5/pdf/{id}', [CoChairController::class, 'Cat5Pdf'])->name('cat5cochair.pdf');
    Route::get('/cat6/pdf/{id}', [CoChairController::class, 'Cat6Pdf'])->name('cat6cochair.pdf');
    Route::get('/cat7/pdf/{id}', [CoChairController::class, 'Cat7Pdf'])->name('cat7cochair.pdf');
    Route::get('/cat7a/pdf/{id}', [CoChairController::class, 'Cat7aPdf'])->name('cat7acochair.pdf');
    Route::get('/cat8/pdf/{id}', [CoChairController::class, 'Cat8Pdf'])->name('cat8cochair.pdf');
    Route::get('/cat8a/pdf/{id}', [CoChairController::class, 'Cat8aPdf'])->name('cat8acochair.pdf');

    Route::post('/application/forward', [CoChairController::class, 'AppCoChairForword'])->name('app.cochair.forward');

    Route::post('/cat1UpdateCoChairStatus', [CoChairController::class, 'updateCat1']);
    Route::post('/cat2UpdateCoChairStatus', [CoChairController::class, 'updateCat2']);
    Route::post('/cat3UpdateCoChairStatus', [CoChairController::class, 'updateCat3']);
    Route::post('/cat4UpdateCoChairStatus', [CoChairController::class, 'updateCat4']);
    Route::post('/cat5UpdateCoChairStatus', [CoChairController::class, 'updateCat5']);
    Route::post('/cat6UpdateCoChairStatus', [CoChairController::class, 'updateCat6']);
    Route::post('/cat7ProposalUpdateCoChairStatus', [CoChairController::class, 'updateCat7Proposal']);
    Route::post('/cat7FinalUpdateCoChairStatus', [CoChairController::class, 'updateCat7Final']);
    Route::post('/cat8UpdateCoChairStatus', [CoChairController::class, 'updateCat8']);

    // Category 1 and 2 history view for co-chair
    Route::get('/app/history/coChairView{empNo}', [CoChairController::class, "appHistoryCoChairView"])->name('app.History.CoChairView');

});//system permission route list

Route::middleware('auth','role:admin|vc')->prefix('vc')->group(function () {

    Route::get('/index', [VcController::class, 'AppView'])->name('app.vc.view');
    Route::get('/application/view/{refNo}', [VcController::class, "AppShow"])->name('app.vc.show');

    Route::get('/cat1/pdf/{id}', [VcController::class, 'Cat1Pdf'])->name('cat1vc.pdf');
    Route::get('/cat1a/pdf/{id}', [VcController::class, 'Cat1aPdf'])->name('cat1avc.pdf');
    Route::get('/cat2/pdf/{id}', [VcController::class, 'Cat2Pdf'])->name('cat2vc.pdf');
    Route::get('/cat3/pdf/{id}', [VcController::class, 'Cat3Pdf'])->name('cat3vc.pdf');
    Route::get('/cat4/pdf/{id}', [VcController::class, 'Cat4Pdf'])->name('cat4vc.pdf');
    Route::get('/cat5/pdf/{id}', [VcController::class, 'Cat5Pdf'])->name('cat5vc.pdf');
    Route::get('/cat6/pdf/{id}', [VcController::class, 'Cat6Pdf'])->name('cat6vc.pdf');
    Route::get('/cat7/pdf/{id}', [VcController::class, 'Cat7Pdf'])->name('cat7vc.pdf');
    Route::get('/cat7a/pdf/{id}', [VcController::class, 'Cat7aPdf'])->name('cat7avc.pdf');
    Route::get('/cat8/pdf/{id}', [VcController::class, 'Cat8Pdf'])->name('cat8vc.pdf');
    Route::get('/cat8a/pdf/{id}', [VcController::class, 'Cat8aPdf'])->name('cat8avc.pdf');

    Route::post('/application/forward', [VcController::class, 'AppVCForword'])->name('app.vc.forward');

});


Route::middleware('auth','role:admin|library')->prefix('librarian')->group(function () {
    Route::get('/librarian/list', [LibrarianController::class, 'librarianOpen'])->name('librarian.list.open');
    Route::get('/librarian/details/open/{refNo}', [LibrarianController::class, "librarianDetails"])->name('librarian.details.open');

    //Route::post('/LibrarianSaveItem',[LibrarianController::class, 'saveItem']);
    Route::post('librarian/item/save',[LibrarianController::class,'libSaveItem'])->name('librarian.item.save');
    Route::post('librarian/store',[LibrarianController::class,'libstore'])->name('librarian.store');

    Route::get('/cat1/pdf/{id}', [LibrarianController::class, 'Cat1Pdf'])->name('lib.cat1.pdf');
    Route::get('/cat1a/pdf/{id}', [LibrarianController::class, 'Cat1aPdf'])->name('lib.cat1a.pdf');

    Route::get('/approved/list', [LibrarianController::class, 'librarianApprovedList'])->name('librarian.approved.list');
    Route::get('/rejected/list', [LibrarianController::class, 'librarianRejectedList'])->name('librarian.rejected.list');

    Route::get('/cat1/approved/list', [LibrarianController::class, 'Cat1ApprovedListAll'])->name('cat1.approved.list.all');
    Route::get('/cat1/final/view/{refNo}', [LibrarianController::class, "Cat1AppFinalView"])->name('cat1.app.final.view');
    Route::post('/cat1/publish/year/store',[LibrarianController::class,'Cat1PublishYearStore'])->name('cat1.publish.year.store');


});

Route::middleware('auth','role:admin|sc|ra')->prefix('final')->group(function () {

    Route::get('/approved/list/{id}', [FinalController::class, 'AppApprovedList'])->name('app.approved.list');
    Route::get('/rejected/list/{id}', [FinalController::class, 'AppRejectedList'])->name('app.rejected.list');
    Route::get('/application/view/{refNo}', [FinalController::class, "AppShowFinal"])->name('app.final.show');

    Route::get('/cat1/pdf/{id}', [FinalController::class, 'Cat1Pdf'])->name('cat1final.pdf');
    Route::get('/cat1a/pdf/{id}', [FinalController::class, 'Cat1aPdf'])->name('cat1afinal.pdf');
    Route::get('/cat2/pdf/{id}', [FinalController::class, 'Cat2Pdf'])->name('cat2final.pdf');
    Route::get('/cat3/pdf/{id}', [FinalController::class, 'Cat3Pdf'])->name('cat3final.pdf');
    Route::get('/cat4/pdf/{id}', [FinalController::class, 'Cat4Pdf'])->name('cat4final.pdf');
    Route::get('/cat5/pdf/{id}', [FinalController::class, 'Cat5Pdf'])->name('cat5final.pdf');
    Route::get('/cat6/pdf/{id}', [FinalController::class, 'Cat6Pdf'])->name('cat6final.pdf');
    Route::get('/cat7/pdf/{id}', [FinalController::class, 'Cat7Pdf'])->name('cat7final.pdf');
    Route::get('/cat7a/pdf/{id}', [FinalController::class, 'Cat7aPdf'])->name('cat7afinal.pdf');
    Route::get('/cat8/pdf/{id}', [FinalController::class, 'Cat8Pdf'])->name('cat8final.pdf');
    Route::get('/cat8a/pdf/{id}', [FinalController::class, 'Cat8aPdf'])->name('cat8afinal.pdf');

    Route::match(['get', 'post'],'/application/process', [FinalController::class, 'AppProcess'])->name('app.process');
    Route::get('/process/reverse/{id}', [FinalController::class, 'AppProcessReverse'])->name('app.process.reverse');

    Route::get('app/completed/list', [FinalController::class, 'AppCompletedList'])->name('app.completed.list');
    Route::get('/app/final/view/{refNo}', [FinalController::class, "AppFinalView"])->name('app.final.view');
    Route::post('/app/final/submit', [FinalController::class, 'AppFinalSubmit'])->name('app.final.submit');

    Route::get('all/approved/list', [FinalController::class, 'AppApprovedListAll'])->name('app.approved.list.all');
    Route::get('all/rejected/list', [FinalController::class, 'AppRejectedListAll'])->name('app.rejected.list.all');

    //Route::get('pending/app/reminder', [FinalController::class, 'PendingAppReminder'])->name('pending.app.reminder');

});

require __DIR__.'/auth.php';





