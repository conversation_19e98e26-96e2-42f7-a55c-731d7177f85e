<?php $__env->startSection('admin'); ?>

<section class="content">

<!-- Default box -->
<div class="card" >
  <div class="card-body">
    <div class="row">
      <div class="col-12 col-md-12 col-lg-12 order-2 order-md-1">
        <div class="row">

          <div class="col-12 col-sm-1">
          </div>
          <div class="col-12 col-sm-10">
            <h2 class="text-center font-weight-bold" style="color: #A82727; font-weight: bold;">Research Allowance - <?php echo e(date('Y')); ?></h2>
          </div>
          <div class="col-12 col-sm-1">

          </div>
        </div>
       <br>

       <div class="container">
        <div class="row justify-content-center">

          <div class="col-md-5 col-sm-5 mb-3"> <!-- Use col-sm-6 for smaller screens -->
          <form method="post" action="<?php echo e(route('careateNewApplication')); ?>" >
          <?php echo csrf_field(); ?>

           <!-- $applicationcount != $findFinalDecisionCount -->
            <button type="submit" href="" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white" >
              <div class="icon">
                <i class="fa fa-book" style="font-size:40px"></i>
              </div>
              <span style="font-size:25px">Apply Now</span><br>
              <span style="font-size:15px;">To view application and start the application process &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
          </button>
          </form>
          </div>


          <div class="col-md-5 col-sm-5 text-center"> <!-- Use col-sm-6 for smaller screens -->
            <a type="button" href="<?php echo e(route('submittedAppHistory')); ?>" class="btn btn-block btn-outline-danger btn-lg p-2 p-sm-4 btn-hover-white">
              <div class="icon">
                <i class="fa fa-history" style="font-size:40px"></i>
              </div>
              <span style="font-size:25px">Application History</span><br>
              <span style="font-size:15px;">To view the previous applications and their status &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            </a>
          </div>


        </div>
      </div>
      <br>
        <div class="container">
            <div class="row">
              <div class="col-md-1"></div> <!-- Extra space on the left for larger screens -->
              <div class="col-md-10 col-sm-12"> <!-- On large screens, use col-lg-8. On smaller screens, use full width col-md-12 -->
                <div class="post">
                  
                  
                  <ul>
                    <li class=" text-justify " style="font-size:15px;">To be eligible for the research allowance for this year, provide the information and evidence requested in Category 1-8.</li>
                    <li class=" text-justify " style="font-size:15px;">Research proposals in Category 7 should be submitted during the first quarter of the year. <a href="<?php echo e(asset('Research-Proposal-Format.pdf')); ?>" download="Research-Proposal-Format.pdf">(Download the format)</a></li>
                    <!-- <li class=" text-justify " style="font-size:15px;">Click the “<span class="text-danger">Add</span>" button after entering data in each category.</li> -->
                    <!-- <li class=" text-justify" style="font-size:15px;">We recommend "<span class="text-danger">Save as Draft</span>" the application during the application process.</li> -->
                    <!-- <li class=" text-justify" style="font-size:15px;">You are allowed to access and edit the application after saving it until you "<span class="text-danger">Submit</span>" it.</li> -->
                    <li class=" text-justify" style="font-size:15px;">You must "<span class="text-danger">Submit</span>" the application after furnishing all the data. You will receive an email confirming your successful submission.</li>
                    <li class=" text-justify" style="font-size:15px;">You will not be able to edit the application after submitting it.</li>
                    <li class=" text-justify" style="font-size:15px;">All future announcements will be notified via your email only.</li>
                    <li class=" text-justify" style="font-size:15px;">Read the <b><a href="<?php echo e(url('/details/instruction')); ?>" target="_blank">Instructions</a></b> for more information regarding the application process and prerequisites.</li>
                </ul>
                </div>
              </div>
              <div class="col-md-1"></div> <!-- Extra space on the right for larger screens -->
            </div>
          </div>

      </div>
    </div>
  </div>
  <!-- /.card-body -->
</div>
<!-- /.card -->

</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\research_allowness\resources\views/frontend/application_new/index.blade.php ENDPATH**/ ?>