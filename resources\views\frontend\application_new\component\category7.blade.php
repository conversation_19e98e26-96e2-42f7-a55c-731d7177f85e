@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-lightbulb-o"></i> Category 7: Research Proposals
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category7Form" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Instructions -->
        <div class="alert alert-warning">
          <h5><i class="icon fa fa-exclamation-triangle"></i> Important Notice</h5>
          Research proposals should be submitted during the first quarter of the year.
          <br><a href="{{ asset('Research-Proposal-Format.pdf') }}" download="Research-Proposal-Format.pdf" class="btn btn-sm btn-outline-primary mt-2">
            <i class="fa fa-download"></i> Download Proposal Format
          </a>
        </div>

        <!-- Proposal Title -->
        <div class="form-group row">
          <label for="proposalTitle" class="col-sm-3 col-form-label">
            Proposal Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="proposalTitle" name="proposal_title" rows="2"
                      placeholder="Enter the title of your research proposal" required></textarea>
          </div>
        </div>

        <!-- Research Area -->
        <div class="form-group row">
          <label for="researchArea" class="col-sm-3 col-form-label">
            Research Area <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="researchArea" name="research_area"
                   placeholder="Enter your research area or field" required>
          </div>
        </div>

        <!-- Proposal Type -->
        <div class="form-group row">
          <label for="proposalType" class="col-sm-3 col-form-label">
            Proposal Type <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="proposalType" name="proposal_type" required>
              <option value="">Select Proposal Type</option>
              <option value="New Proposal">New Research Proposal</option>
              <option value="Continuation">Continuation of Previous Research</option>
              <option value="Collaborative">Collaborative Research</option>
              <option value="Interdisciplinary">Interdisciplinary Research</option>
            </select>
          </div>
        </div>

        <!-- Duration -->
        <div class="form-group row">
          <label for="duration" class="col-sm-3 col-form-label">
            Proposed Duration <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <div class="row">
              <div class="col-sm-6">
                <input type="date" class="form-control" id="startDate" name="start_date" required>
                <small class="form-text text-muted">Proposed Start Date</small>
              </div>
              <div class="col-sm-6">
                <input type="date" class="form-control" id="endDate" name="end_date" required>
                <small class="form-text text-muted">Proposed End Date</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Budget -->
        <div class="form-group row">
          <label for="budget" class="col-sm-3 col-form-label">
            Proposed Budget <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text">LKR</span>
              </div>
              <input type="number" class="form-control" id="budget" name="budget"
                     placeholder="Enter proposed budget" step="0.01" required>
            </div>
          </div>
        </div>

        <!-- Objectives -->
        <div class="form-group row">
          <label for="objectives" class="col-sm-3 col-form-label">
            Research Objectives <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="objectives" name="objectives" rows="4"
                      placeholder="Describe the main objectives of your research" required></textarea>
          </div>
        </div>

        <!-- Methodology -->
        <div class="form-group row">
          <label for="methodology" class="col-sm-3 col-form-label">
            Methodology <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="methodology" name="methodology" rows="4"
                      placeholder="Describe your research methodology" required></textarea>
          </div>
        </div>

        <!-- Expected Outcomes -->
        <div class="form-group row">
          <label for="expectedOutcomes" class="col-sm-3 col-form-label">
            Expected Outcomes <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="expectedOutcomes" name="expected_outcomes" rows="3"
                      placeholder="Describe the expected outcomes and impact" required></textarea>
          </div>
        </div>

        <!-- Proposal Document Upload -->
        <div class="form-group row">
          <label for="proposalDocument" class="col-sm-3 col-form-label">
            Proposal Document <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="proposalDocument" name="proposal_document"
                   accept=".pdf,.doc,.docx" required>
            <small class="form-text text-muted">Upload complete research proposal (PDF, DOC, DOCX) - Max: 20MB</small>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="button" class="btn btn-info mr-2" onclick="saveDraft()">
              <i class="fa fa-save"></i> Save as Draft
            </button>
            <button type="submit" class="btn btn-success">
              <i class="fa fa-plus"></i> Add Proposal
            </button>
          </div>
        </div>
      </form>

      <!-- Proposals List -->
      <div class="mt-4">
        <h5>Added Research Proposals</h5>
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead class="thead-dark">
              <tr>
                <th>No.</th>
                <th>Title</th>
                <th>Research Area</th>
                <th>Type</th>
                <th>Duration</th>
                <th>Budget</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="proposalsTableBody">
              <tr>
                <td colspan="7" class="text-center text-muted">No research proposals added yet</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category7Form').reset();
}

function saveDraft() {
    alert('Draft saved successfully!');
}
</script>

@endsection
