@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-file-text-o"></i> Category 1: Indexed Journal Publications
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category1Form" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Instructions -->
        <div class="alert alert-info">
          <h5><i class="icon fa fa-info"></i> Instructions</h5>
          Please provide details of your indexed journal publications for the current year.
        </div>

        <!-- Publication Title -->
        <div class="form-group row">
          <label for="publicationTitle" class="col-sm-3 col-form-label">
            Publication Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="publicationTitle" name="publication_title"
                   placeholder="Enter the title of your publication" required>
          </div>
        </div>

        <!-- Journal Name -->
        <div class="form-group row">
          <label for="journalName" class="col-sm-3 col-form-label">
            Journal Name <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="journalName" name="journal_name"
                   placeholder="Enter the journal name" required>
          </div>
        </div>

        <!-- Indexing Service -->
        <div class="form-group row">
          <label for="indexingService" class="col-sm-3 col-form-label">
            Indexing Service <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="indexingService" name="indexing_service" required>
              <option value="">Select Indexing Service</option>
              <option value="SCI">SCI (Science Citation Index)</option>
              <option value="SSCI">SSCI (Social Sciences Citation Index)</option>
              <option value="AHCI">AHCI (Arts & Humanities Citation Index)</option>
              <option value="Scopus">Scopus</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>

        <!-- Publication Status -->
        <div class="form-group row">
          <label for="publicationStatus" class="col-sm-3 col-form-label">
            Publication Status <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="publicationStatus" name="publication_status" required>
              <option value="">Select Status</option>
              <option value="Published">Published</option>
              <option value="Accepted">Accepted for Publication</option>
              <option value="In Press">In Press</option>
            </select>
          </div>
        </div>

        <!-- Volume, Issue, Pages -->
        <div class="form-group row">
          <label for="volumeIssuePages" class="col-sm-3 col-form-label">
            Volume, Issue & Pages <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="volumeIssuePages" name="volume_issue_pages"
                   placeholder="e.g., Vol. 15(2), pp. 123-135" required>
          </div>
        </div>

        <!-- Year of Publication -->
        <div class="form-group row">
          <label for="yearOfPublication" class="col-sm-3 col-form-label">
            Year of Publication <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="number" class="form-control" id="yearOfPublication" name="year_of_publication"
                   min="2020" max="{{ date('Y') }}" value="{{ date('Y') }}" required>
          </div>
        </div>

        <!-- Publication File Upload -->
        <div class="form-group row">
          <label for="publicationFile" class="col-sm-3 col-form-label">
            Publication File <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="publicationFile" name="publication_file"
                   accept=".pdf,.doc,.docx" required>
            <small class="form-text text-muted">Upload PDF, DOC, or DOCX file (Max: 10MB)</small>
          </div>
        </div>

        <!-- Evidence File Upload -->
        <div class="form-group row">
          <label for="evidenceFile" class="col-sm-3 col-form-label">
            Indexing Evidence <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="evidenceFile" name="evidence_file"
                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
            <small class="form-text text-muted">
              Upload evidence of indexing (PDF, DOC, DOCX, JPG, PNG) - Max: 5MB
              <br><a href="#" target="_blank">View Sample Evidence File</a>
            </small>
          </div>
        </div>

        <!-- URL (Optional) -->
        <div class="form-group row">
          <label for="publicationUrl" class="col-sm-3 col-form-label">
            Publication URL <small>(Optional)</small>
          </label>
          <div class="col-sm-9">
            <input type="url" class="form-control" id="publicationUrl" name="publication_url"
                   placeholder="https://example.com/publication">
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="button" class="btn btn-info mr-2" onclick="saveDraft()">
              <i class="fa fa-save"></i> Save as Draft
            </button>
            <button type="submit" class="btn btn-success">
              <i class="fa fa-plus"></i> Add Publication
            </button>
          </div>
        </div>
      </form>

      <!-- Publications List -->
      <div class="mt-4">
        <h5>Added Publications</h5>
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead class="thead-dark">
              <tr>
                <th>No.</th>
                <th>Title</th>
                <th>Journal</th>
                <th>Indexing Service</th>
                <th>Status</th>
                <th>Volume/Issue/Pages</th>
                <th>Year</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="publicationsTableBody">
              <!-- Dynamic content will be added here -->
              <tr>
                <td colspan="8" class="text-center text-muted">No publications added yet</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category1Form').reset();
}

function saveDraft() {
    // Add save draft functionality
    alert('Draft saved successfully!');
}
</script>

@endsection
