<div class="panel panel-default">
    <a class="toggleLinks" data-toggle="collapse" data-parent="#accordion" href="#collapse3">
        <div class="panel-heading">
            <h5 class="panel-title" id="cat3PanelTitle">
            Category 3: Research Grants
            </h5>
        </div>
    </a>
<div id="collapse3" class="panel-collapse collapse">
    <div class="panel-body">
    <!-- *******************Category three form start****************** -->
    <form style="display: <?php echo $partThreeData !== null ? "none": "block" ?>" id="cat3form">
        <!-- 3.1 -->
        <div class="form-group row" style="margin-bottom: 0px; padding-bottom:0px;">
            <div class="form-group col-md-12">
                <label for="lableResearchGrant" id="lableResearchGrant">If you have received a research grant from an international organization/
                     an approved funding body in Sri Lanka/ University grant and commenced activity,</label>
            </div>
            <div class="form-group col-md-1" style="display:none;" >
                <input type="hidden" name="receivedGrant" class="form-control" id="inputResearchGrant" oninput="validateCategory03()" placeholder="" value="Yes">
                <p id="cat3GrantErrorMsg" class="allInputErrorMsg"> *Can't empty this field* </p>
            </div>
        </div>
        <!-- Grant details -->
        <div class="form-group row" >

            <label for="lableDetails" class="col-sm-2 col-form-label" id="lableGrantDetails">Grant Details <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="grantDetails" id="inputGrantDetails" oninput="validateCategory03()" placeholder="Grant Details" rows="4" required></textarea >
                <p id="cat3GrantDetailsErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter Grant Details* </p>
            </div>
        </div>
        <!-- Funded by -->
        <div class="form-group row">
            <label for="lableFundedBy" class="col-sm-2 col-form-label" id="lableFundedBy">Funded by <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="text" name="fundedBy" class="form-control" id="inputFundedBy" oninput="validateCategory03()" placeholder="Funded by" required>
                <p id="cat3FundedByErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter Funded by* </p>
            </div>
        </div>
        <!-- Category 3 file upload -->
        <div class="form-group row">
            <label for="inputFile" id="cat3fileUploadLable" class="col-sm-2 col-form-label">Upload the proof document<span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="file" name="uploadFile" oninput="validatePDFCategory03(), validateCategory03()" class="form-control" id="cat3UploadFile" accept=".pdf">
                <p id="cat3FileUploadErrorMsg" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
            </div>
        </div>
        <!-- Category three draft button -->
        <div class="form-group row">
            <div class="col-sm-6 col-md-10"></div>
            <div class="col-sm-3 col-md-1">
                <button type="button" name="categoryThreeAdd" id="categoryThreeAddBtn" onclick="AddPartThreeRecoredToDb()"  class="btn btn-primary add-btn">Submit</button>
            </div>
            <!-- Category Three Clear button -->
            <div class="col-sm-3 col-md-1">
                <button type="button" class="btn btn-warning clear-btn" id="cat3ClearBtn" onclick="cat3Clear()">Clear</button>
            </div>
            
        </div>
    </form>
    <!-- *******************form end******************** -->
    <!-- Table 3 start-->
    <div class="form-group row" style="overflow-x:auto; display:none;">
        <table id="catagoryThreeTable" class="table table-bordered">
            <thead>
                <tr>
                    <th scope="col">No</th>
                    <th scope="col">Grant Details</th>
                    <th scope="col">Funded By</th>
                    <th scope="col">File</th>
                    <th scope="col">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php $partThreerowcount = 0; ?>
                    <?php if($partThreeData !== null): ?>
                        <?php $__currentLoopData = $partThreeData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php $partThreerowcount = ++$partThreerowcount; ?>
                            <tr>
                                <td><?php echo e($partThreerowcount); ?></td>
                                <td><?php echo e($row->grantDetails); ?></td>
                                <td><?php echo e($row->fundedBy); ?></td>
                                <td>
                                    <?php if($row->uploadFile): ?>
                                    <span class="badge badge-pill badge-success">Uploaded</span>
                                    <?php else: ?>
                                    <span class="badge badge-pill badge-dark">No Attachment</span>
                                    <?php endif; ?>
                                </td>
                                <td><button class="btn btn-sm btn-danger" onclick="deleteDBrowPart3('<?php echo e($row->id); ?>','<?php echo e($partThreerowcount); ?>','1')">Delete</button></td>
                            </tr>

                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <?php endif; ?>
                <!-- data will be add -->
            </tbody>
        </table>
    </div>
    <!-- Table 3 end -->
    </div>
  </div>
</div>

<!-- Caegory Three lower table -->
<script>
    // Validate All fields are required
     function validateCategory03() {
        // Get form values
        var researchGrant = document.getElementById('inputResearchGrant').value;
        var grantDetails = document.getElementById('inputGrantDetails').value;
        var fundedBy = document.getElementById('inputFundedBy').value;
        var cat3File = document.getElementById('cat3UploadFile').value;
        // get values for value is set or not
        const panelTitle = document.getElementById('cat3PanelTitle');
        const submitButton = document.getElementById('finalSubmitBtn');
        const saveDraftButton = document.getElementById('finalSaveDraftBtn');
        // Get elements by class name
        var toggleLinks = document.getElementsByClassName('toggleLinks');


        //Validate Catagory 3 research Grant
        var isValidateCat3 = true;
        if(researchGrant ==""){
            isValidateCat3 = false;
            document.getElementById("cat3GrantErrorMsg").style.display = "block";
        }else{
            document.getElementById("cat3GrantErrorMsg").style.display = "none";
        }

        //Validate Catagory 3 grant Details
        if(grantDetails ==""){
            isValidateCat3 = false;
            document.getElementById("cat3GrantDetailsErrorMsg").style.display = "block";
        }else{
            document.getElementById("cat3GrantDetailsErrorMsg").style.display = "none";
        }

        //Validate Catagory 3 funded By
        if(fundedBy ==""){
            isValidateCat3 = false;
            document.getElementById("cat3FundedByErrorMsg").style.display = "block";
        }else{
            document.getElementById("cat3FundedByErrorMsg").style.display = "none";
        }

        //Validate Catagory 3 File upload
        if(cat3File ==""){
            isValidateCat3 = false;
            document.getElementById("cat3FileUploadErrorMsg").style.display = "block";
        }else{
            document.getElementById("cat3FileUploadErrorMsg").style.display = "none";
        }

        // check for the value is added or not
        if(grantDetails != "" || fundedBy != ""){
                // Change the text color of the panel title
                panelTitle.style.color = '#f2820a';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Disable the submit and save draft button
                submitButton.disabled = true;
                saveDraftButton.disabled = true;
            }else{
                // Reset the text color of the panel title
                panelTitle.style.color = '#021691';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Enable the submit button
                submitButton.disabled = false;
                saveDraftButton.disabled = false;
            }

        return isValidateCat3;



    }

    // start Category 3 save to database
    function AddPartThreeRecoredToDb(){
        if(validateCategory03()){
            // Get form values
            var researchGrant = document.getElementById('inputResearchGrant').value;
            var grantDetails = document.getElementById('inputGrantDetails').value;
            var fundedBy = document.getElementById('inputFundedBy').value;
            // get values for value is set or not
            const panelTitle = document.getElementById('cat3PanelTitle');
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');
            // Get elements by class name
            var toggleLinks = document.getElementsByClassName('toggleLinks');

            const cat5File1 = document.getElementById('cat3UploadFile');
            var files = $('#cat3UploadFile')[0].files;
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');

            var uploadedData = new FormData();
            uploadedData.append('grantDetails',grantDetails);
            uploadedData.append('fundedBy',fundedBy);
            uploadedData.append('_token',CSRF_TOKEN);
            uploadedData.append('file',files[0]);

            // Add button disable
            var cat3AddBtn = document.getElementById('categoryThreeAddBtn');
            // cat3AddBtn.disabled = true;

        //   Catagory Three - Ajax request
        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
        Swal.fire({
            title: "Are you sure that you want to submit?",
            text: "You will not be able to edit the application after submitting it",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes. Submit"
            
        }).then((result) => {
            if (result.isConfirmed) {
                // Ajax start
                $.ajax({

                    url: '/createPartThreeRecord',
                    type: 'POST',
                    data: uploadedData,
                    contentType: false,
                    processData: false,
                    dataType: 'JSON',

                    success: function (data) {
                        if(data.status){
                            cat3AddRow(data.insertedId);
                            // Reset the text color of the panel title
                            panelTitle.style.color = '#021691';

                            // Loop through the elements and update the data-toggle attribute
                            for (var i = 0; i < toggleLinks.length; i++) {
                                // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                                toggleLinks[i].setAttribute('data-toggle', 'noName');
                            }

                            // Enable the submit and save draft button
                            submitButton.disabled = false;
                            saveDraftButton.disabled = false;

                            //  If successful, Enable the Add button
                            cat3AddBtn.disabled = false;
                            

                            // Redirect to the application.store route
                            window.location.href = "<?php echo e(route('application.store')); ?>";
                        }

                    },
                    error: function(xhr, status, error) {
                        alert("Somethig went worng");
                    }

                    });
                // Ajax end
                    Swal.fire({
                        title: "Success!",
                        // text: "Your application has been submitted successfuly.",
                        icon: "success"
                });
            }
        });
            
        }

    }
    // end Category 3 save to database

    // Start category 3 update
    // function partThreeRecoredUpdateToDb(){
    //    var updatedId =  document.getElementById("category3UpdatedID").value;
    //     if(validateCategory03()){
    //         // Get form values
    //         var researchGrant = document.getElementById('inputResearchGrant').value;
    //         var grantDetails = document.getElementById('inputGrantDetails').value;
    //         var fundedBy = document.getElementById('inputFundedBy').value;

    //         // alert(updatedId);

    //     //   Catagory Three - Ajax request
    //     var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
    //         $.ajax({

    //             url: '/updatePartThreeRecord',
    //             type: 'POST',
    //             data: {_token: CSRF_TOKEN,
    //                 researchGrant: researchGrant,
    //                 grantDetails: grantDetails,
    //                 fundedBy: fundedBy,
    //                 updatedId: updatedId
    //             },
    //             dataType: 'JSON',

    //             success: function (data) {
    //                 if(data.status){
    //                     cat3UpdateRow(data.updatedId);
    //                 }

    //             },
    //             error: function(xhr, status, error) {
    //                 alert("Somethig went worng");
    //             }

    //         });
    //     }

    // }
    // end cat 3 update

    // Start Category 3 delete each row in DB
    // Delete in the row of database
    function deleteDBrowPart3(deletedId,tblRowId, accNo){
        //   Catagory Three - Ajax request for delete row
        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
            $.ajax({

                url: '/deletePartThreeRecord',
                type: 'POST',
                data: {_token: CSRF_TOKEN,
                    deletedId: deletedId,
                },
                dataType: 'JSON',

                success: function (data) {
                    if(data.status){
                        cat3DeleteRow(tblRowId,accNo);
                    }else{
                        alert("Error");
                    }

                },
                error: function(xhr, status, error) {
                    alert("Somethig went worng");
                }

            });
    }
    // End Category 3 delete

    // Function to delete a row from the table
    function cat3DeleteRow(accNo) {
        if(accNo == '1'){
            catagoryThreeTable.deleteRow(1);
        }else{
            catagoryThreeTable.deleteRow(1);
        }
        // catagoryThreeTable.deleteRow(1);
        cat3ShowPanel();
        // collapse - enable
        var panelTitleID = 'cat3PanelTitle';
        resetAllPanel(panelTitleID);
    }

        // Add new recode
        function cat3AddRow(inseredId){

            // Validate category 3 and add new row
            if(!validateCategory03()){
                return;
            }

            // Get form values
            var researchGrant = document.getElementById('inputResearchGrant').value;
            var grantDetails = document.getElementById('inputGrantDetails').value;
            var fundedBy = document.getElementById('inputFundedBy').value;
            var cat3FileUpload = document.getElementById('cat3UploadFile').value;

            // Get table reference
            var table = document.getElementById('catagoryThreeTable').getElementsByTagName('tbody')[0];

            // Create a new row
            var newRow = table.insertRow();
            // var newRowId = newRow.id;
            // console.log(newRow);

            // Insert cells
            var cell1 = newRow.insertCell(0);
            var cell2 = newRow.insertCell(1);
            var cell3 = newRow.insertCell(2);
            var cell4 = newRow.insertCell(3);
            var cell5 = newRow.insertCell(4);

            // Populate cells with form values
            cell1.innerHTML = table.rows.length; // Auto-increment No column
            cell2.innerHTML = grantDetails;
            cell3.innerHTML = fundedBy;
            cell4.innerHTML = cat3FileUpload ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';

            // alert(newRowId);
            console.log(table.rows.length);

            // Add edit and delete buttons
            cell5.innerHTML = '<span class="btn btn-sm btn-danger" onclick="deleteDBrowPart3('+ inseredId +')">Delete</span>';
            //
            // Clear form values
            document.getElementById('inputResearchGrant').value = 'Yes';
            document.getElementById('inputGrantDetails').value = '';
            document.getElementById('inputFundedBy').value = '';
            document.getElementById('cat3UploadFile').value = '';

            // Hide the form fields
            document.getElementById('inputResearchGrant').style.display = 'none';
            document.getElementById('lableResearchGrant').style.display = 'none';
            document.getElementById('inputGrantDetails').style.display = 'none';
            document.getElementById('lableGrantDetails').style.display = 'none';
            document.getElementById('inputFundedBy').style.display = 'none';
            document.getElementById('lableFundedBy').style.display = 'none';
            document.getElementById('categoryThreeAddBtn').style.display = 'none';
            document.getElementById('cat3fileUploadLable').style.display = 'none';
            document.getElementById('cat3ClearBtn').style.display = 'none';
            document.getElementById('cat3UploadFile').style.display = 'none';
        }

        // // Category 3 update
        // function cat3UpdateRow(updatedId){

        //     // Validate category 3 and add new row
        //     if(!validateCategory03()){
        //         return;
        //     }

        //     // Get form values
        //     var researchGrant = document.getElementById('inputResearchGrant').value;
        //     var grantDetails = document.getElementById('inputGrantDetails').value;
        //     var fundedBy = document.getElementById('inputFundedBy').value;

        //     // Get table reference
        //     var table = document.getElementById('catagoryThreeTable').getElementsByTagName('tbody')[0];

        //     // Create a new row
        //     var newRow = table.insertRow();
        //     // var newRowId = newRow.id;
        //     // console.log(newRow);

        //     // Insert cells
        //     var cell1 = newRow.insertCell(0);
        //     var cell2 = newRow.insertCell(1);
        //     var cell3 = newRow.insertCell(2);
        //     var cell4 = newRow.insertCell(3);
        //     var cell5 = newRow.insertCell(4);

        //     // Populate cells with form values
        //     cell1.innerHTML = table.rows.length; // Auto-increment No column
        //     cell2.innerHTML = researchGrant;
        //     cell3.innerHTML = grantDetails;
        //     cell4.innerHTML = fundedBy;

        //     // alert(newRowId);
        //     console.log(table.rows.length);

        //     // Add edit and delete buttons
        //     cell5.innerHTML = '<span class="btn btn-success" onclick="editRow(this)">Edit</span> ' +
        //                     '<span class="btn btn-danger" onclick="deleteDBrowPart3('+ updatedId +')">Delete</span>';

        //     // Clear form values
        //     document.getElementById('inputResearchGrant').value = '';
        //     document.getElementById('inputGrantDetails').value = '';
        //     document.getElementById('inputFundedBy').value = '';

        //     // Hide the form fields
        //     document.getElementById('inputResearchGrant').style.display = 'none';
        //     document.getElementById('lableResearchGrant').style.display = 'none';
        //     document.getElementById('inputGrantDetails').style.display = 'none';
        //     document.getElementById('lableGrantDetails').style.display = 'none';
        //     document.getElementById('inputFundedBy').style.display = 'none';
        //     document.getElementById('lableFundedBy').style.display = 'none';
        //     document.getElementById('categoryThreeAddBtn').style.display = 'none';
        //     document.getElementById('categoryThreeCancalBtn').style.display = 'none';
        //     document.getElementById('categoryThreeUpdateBtn').style.display = 'none';
        // }
        // // end update

    // Category 3 edit button
    function editRow(button, updatedId) {
        // Get the row containing the button
        var row = button.parentNode.parentNode;

        document.getElementById("category3UpdatedID").value = updatedId;

        // Get values from the row
        var researchGrant = row.cells[1].innerHTML;
        var grantDetails = row.cells[2].innerHTML;
        var fundedBy = row.cells[3].innerHTML;

        // Set form values
        document.getElementById('inputResearchGrant').value = researchGrant;
        document.getElementById('inputGrantDetails').value = grantDetails;
        document.getElementById('inputFundedBy').value = fundedBy;

        // Delete the row
        row.parentNode.removeChild(row);

        // Show the form fields
        document.getElementById('inputResearchGrant').style.display = 'block';
        document.getElementById('lableResearchGrant').style.display = 'block';
        document.getElementById('inputGrantDetails').style.display = 'block';
        document.getElementById('lableGrantDetails').style.display = 'block';
        document.getElementById('inputFundedBy').style.display = 'block';
        document.getElementById('lableFundedBy').style.display = 'block';
        document.getElementById('categoryThreeAddBtn').style.display = 'none';
        document.getElementById('categoryThreeUpdateBtn').style.display = 'block';
        document.getElementById('categoryThreeCancalBtn').style.display = 'block';
    }

    // Category 3 Show panel
    function cat3ShowPanel() {

        document.getElementById('cat3form').style.display = 'block';
        document.getElementById('cat3ClearBtn').style.display = 'block';
        // Show the form fields
        document.getElementById('inputResearchGrant').style.display = 'block';
        document.getElementById('lableResearchGrant').style.display = 'block';
        document.getElementById('inputGrantDetails').style.display = 'block';
        document.getElementById('lableGrantDetails').style.display = 'block';
        document.getElementById('inputFundedBy').style.display = 'block';
        document.getElementById('lableFundedBy').style.display = 'block';
        document.getElementById('categoryThreeAddBtn').style.display = 'block';
        document.getElementById('cat3fileUploadLable').style.display = 'block';
        document.getElementById('cat3UploadFile').style.display = 'block';

    }

    // Validata upload only .pdf for Category Three
    function validatePDFCategory03() {
        // Get the file input element
        var fileInput = document.getElementById('cat3UploadFile');

        // Get the selected file
        var selectedFile = fileInput.files[0];

        // Maximum file size in bytes (35MB)
        var maxSize = 35 * 1024 * 1024;

        // Check if a file is selected
        if (selectedFile) {
            // Get the file extension
            var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

            // Check if the file extension is not 'pdf'
            if (fileExtension !== 'pdf') {
                // Display error message for incorrect file type
                document.getElementById('cat3FileUploadErrorMsg').innerText = '*Please upload a .pdf file.*';
                document.getElementById('cat3FileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            } 
            // Check if file size exceeds 35MB
            else if (selectedFile.size > maxSize) {
                // Display error message for file size exceeding 35MB
                document.getElementById('cat3FileUploadErrorMsg').innerText = '*File size should not exceed 35MB.*';
                document.getElementById('cat3FileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            } 
            else {
                // Hide error message
                document.getElementById('cat3FileUploadErrorMsg').style.display = 'none';
            }
        }
    }

     // Category 3 Clear button
     function cat3Clear(){
            document.getElementById("inputGrantDetails").value = '';
            document.getElementById("inputFundedBy").value = '';
            document.getElementById('cat3UploadFile').value = '';
            // Clear error msg
            document.getElementById("cat3GrantDetailsErrorMsg").style.display = "none";
            document.getElementById("cat3FundedByErrorMsg").style.display = "none";
            document.getElementById("cat3FileUploadErrorMsg").style.display = "none";

            //  When it clicks the clear button, Enable the Add button
            var cat3AddBtn = document.getElementById('categoryThreeAddBtn');
            cat3AddBtn.disabled = false;

            var panelTitleID = 'cat3PanelTitle';
            resetAllPanel(panelTitleID);

        }
</script>
<?php /**PATH D:\Development\research_allowness\resources\views/frontend/application/component/category03.blade.php ENDPATH**/ ?>