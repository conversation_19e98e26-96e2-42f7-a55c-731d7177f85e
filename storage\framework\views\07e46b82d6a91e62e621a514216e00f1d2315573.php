<div class="panel panel-default">
    <a class="toggleLinks" data-toggle="collapse" data-parent="#accordion" href="#collapse4">
        <div class="panel-heading">
            <h5 class="panel-title" id="cat4PanelTitle">
            Category 4: Other Scholarly Work
            </h5>
        </div>
    </a>
<div id="collapse4" class="panel-collapse collapse">
    <div class="panel-body">
    <!-- *******************Category four form start****************** -->
    <form style="display: <?php echo $partFourData !== null ? "none" : "block" ?>;" id="cat4form">
    <?php echo csrf_field(); ?>
        <!-- 4.1 -->
        <p>Have you <strong>completed/ produced/ published</strong> any of the following academic work during the year <?php echo $currentYear?>?</p>
        <div class="form-group row">
            <label for="academicWorkLable" class="col-sm-2 col-form-label">Academic Work <span class="asterisk">*</span></label>
            <div class="col-sm-8">
                <select name="academicWork" class="form-control" oninput="validateCategory04()" id="academicWorkSelect" aria-label="Default select example">
                    <option style="background-color: gray; color: white;" value="Academic Work" selected>Academic Work</option>
                    <option value="Review Article">Review Article</option>
                    <option value="Editorial">Editorial</option>
                    <option value="Case Report">Case Report</option>
                    <option value="Letter to the Editor">Letter to the Editor</option>
                    <option value="Debating Document">Debating Document</option>
                    <option value="Text Book">Text Book</option>
                    <option value="Research Thesis">Research Thesis</option>
                    <option value="Book/ Book Chapter">Book/ Book Chapter</option>
                    <option value="Any other Creative Work(Ex: Drama, Music etc)">Any Other Creative Work (Ex: Drama, Music etc.)</option>
                </select>
                <p id="cat4AcademicWorkErrorMsg" style="display:none" class="allInputErrorMsg"> *Please select Relevant Academic work* </p>
            </div>


            <!-- <div class="col-sm-2">
                <input name="publicationDate" type="date" class="form-control" oninput="validateCategory04()" id="inputCategory4Date" required>
                <p id="cat4PublishedDateErrorMsg" style="display:none" class="allInputErrorMsg"> *Please Enter the Date of Published* </p>
            </div>
            <div class="col-sm-6">
                <input name="details" type="text" class="form-control" oninput="validateCategory04()" id="inputCategory4Details" placeholder="Remarks" required>
                <p id="cat4DetailsErrorMsg" style="display:none" class="allInputErrorMsg"> *Please Enter Remarks* </p>
            </div> -->
        </div>
        <div class="form-group row">
            <label for="publishedDate" class="col-sm-2 col-form-label">Date of completed/ produced/ published <span class="asterisk">*</span></label>
            <div class="col-sm-2">
                <input name="publicationDate" type="date" class="form-control" oninput="validateCategory04()" id="inputCategory4Date" required>
                <p id="cat4PublishedDateErrorMsg" style="display:none" class="allInputErrorMsg"> *Please Enter the Date of Published* </p>
            </div>
            <div class="col-sm-6">
                <input name="details" type="text" class="form-control" oninput="validateCategory04()" id="inputCategory4Details" placeholder="Remarks" required>
                <p id="cat4DetailsErrorMsg" style="display:none" class="allInputErrorMsg"> *Please Enter Remarks* </p>
            </div>
        </div>
        <!-- Category 4 file upload -->
        <div class="form-group row">
            <label for="inputFile" id="cat4fileUploadLable" class="col-sm-2 col-form-label">Upload the proof document<span class="asterisk">*</span></label>
            <div class="col-sm-8">
                <input type="file" name="uploadFile" oninput="validatePDFCategory04(), validateCategory04()" class="form-control" id="cat4UploadFile" accept=".pdf">
                <p id="cat4FileUploadErrorMsg" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
            </div>
            <!-- Category four add button -->
            <div class="col-sm-1">
                        <button type="button" onclick="AddPartFourRecoredToDb()" id="categoryFourAddBtn" name="categoryFourAdd" class="btn btn-primary add-btn">Submit</button>
            </div>
            <!-- Category Four Clear button -->
            <div class="col-sm-1">
                <button type="button" class="btn btn-warning clear-btn" onclick="cat4Clear()">Clear</button>
            </div>
        </div>
        <div class="form-group row">

            
        </div>
    </form>
    <!-- *******************form end******************** -->
    <!-- Table 4 start-->
    <div class="form-group row" style="overflow-x:auto; display:none;">
        <table id="categoryFourTable" class="table table-bordered">
            <thead>
                <tr>
                    <th scope="col">No</th>
                    <th scope="col">Academic work</th>
                    <th scope="col">Date of Published</th>
                    <th scope="col">Remarks</th>
                    <th scope="col">File</th>
                    <th scope="col">Action</th>
                </tr>
                </thead>
                <tbody>
                    <?php $partFourrowcount = 0; ?>
                        <?php if($partFourData !== null): ?>
                            <?php $__currentLoopData = $partFourData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $partFourrowcount = ++$partFourrowcount; ?>
                                <tr id="partfour<?php echo e($index+1); ?>">
                                    <td><?php echo e($index+1); ?></td>
                                    <td><?php echo e($row->academicWork); ?></td>
                                    <td><?php echo e($row->dateOfPublication); ?></td>
                                    <td><?php echo e($row->details); ?></td>
                                    <td>
                                        <?php if($row->uploadFile): ?>
                                        <span class="badge badge-pill badge-success">Uploaded</span>
                                        <?php else: ?>
                                        <span class="badge badge-pill badge-dark">No Attachment</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><button class="btn btn-sm btn-danger" onclick="deleteDBrowPart4('<?php echo e($row->id); ?>','<?php echo e($index+1); ?>','1')">Delete</button></td>
                                </tr>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <?php endif; ?>
                    <!-- data will be add -->
                </tbody>
        </table>
    </div>
    <!-- Table 4 end -->
    </div>
  </div>
</div>

<!-- Category four lower table -->
<script>
    let categoryFourRowCount = 0;
    var partFourCount = '<?php echo e($partFourCount); ?>';
    function AddRowPartFour(insertedId){
        // Get values from form
        const academicWork = document.querySelector('#academicWorkSelect option:checked').text;
        const datePublished = document.getElementById("inputCategory4Date").value;
        const details = document.getElementById("inputCategory4Details").value;
        const cat4File = document.getElementById("cat4UploadFile").value;
        partFourCount++;

        if(validateCategory04()){
            // Create a new row
            const newRow4 = categoryFourTable.insertRow();

            // Add data to the row
            newRow4.insertCell(0).innerHTML = partFourCount;
            newRow4.insertCell(1).innerHTML = academicWork;
            newRow4.insertCell(2).innerHTML = datePublished;
            newRow4.insertCell(3).innerHTML = details;
            newRow4.insertCell(4).innerHTML = cat4File ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';

            // Add delete button to the row with Bootstrap 'btn-danger' class
            const deleteButtonCell = newRow4.insertCell(5);
            const deleteButton = document.createElement("button");
            deleteButton.textContent = "Delete";
            deleteButton.className = "btn btn-sm btn-danger";
            deleteButton.addEventListener("click", function () {
                // Find the parent row and delete it
                // categoryFourTable.removeChild(newRow4);
                deleteDBrowPart4(insertedId,newRow4);
            });
            deleteButtonCell.appendChild(deleteButton);

            // Clear form fields
            document.getElementById("academicWorkSelect").value = "Academic Work";
            document.getElementById("inputCategory4Date").value = "";
            document.getElementById("inputCategory4Details").value = "";
            document.getElementById("cat4UploadFile").value = "";
            document.getElementById('cat4form').style.display = 'none';
        }
    }

    // Function to delete a row from the table
    function cat4DeleteRow(row,accNo) {
        if(accNo == '1'){
            var rowTemp = document.getElementById("partfour"+row);
            rowTemp.parentNode.removeChild(rowTemp);
        }else{
            categoryFourTable.deleteRow(row.rowIndex);
        }

        document.getElementById('cat4form').style.display = 'block';
        // collapse - enable
        var panelTitleID = 'cat4PanelTitle';
        resetAllPanel(panelTitleID);
    }

    // Delete in the row of database
    function deleteDBrowPart4(deletedId,tblRowId, accNo){
        //   Catagory Four - Ajax request for delete row
        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
            $.ajax({

                url: '/deletePartFourRecord',
                type: 'POST',
                data: {_token: CSRF_TOKEN,
                    deletedId: deletedId,
                },
                dataType: 'JSON',

                success: function (data) {
                    if(data.status){
                        cat4DeleteRow(tblRowId, accNo);
                    }else{
                        alert("Error");
                    }

                },
                error: function(xhr, status, error) {
                    alert("Somethig went worng");
                }

            });
    }
    function AddPartFourRecoredToDb(){
        if(validateCategory04()){
            // Get values from form
            const academicWork = document.querySelector('#academicWorkSelect option:checked').text;
            const datePublished = document.getElementById("inputCategory4Date").value;
            const details = document.getElementById("inputCategory4Details").value;
            const cat4FileUpload = document.getElementById("cat4UploadFile").value;
            // get values for value is set or not
            const panelTitle = document.getElementById('cat4PanelTitle');
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');
            // Get elements by class name
            var toggleLinks = document.getElementsByClassName('toggleLinks');

            // const cat4File = document.getElementById('cat4UploadFile');
            var files = $('#cat4UploadFile')[0].files;
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');

            var uploadedData = new FormData();
            uploadedData.append('academicWork',academicWork);
            uploadedData.append('datePublished',datePublished);
            uploadedData.append('details',details);
            uploadedData.append('_token',CSRF_TOKEN);
            uploadedData.append('file',files[0]);

            // Add button disable
            var cat4AddBtn = document.getElementById('categoryFourAddBtn');
            // cat4AddBtn.disabled = true;

        //   Catagory Four - Ajax request
        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
        Swal.fire({
            title: "Are you sure that you want to submit?",
            text: "You will not be able to edit the application after submitting it",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes. Submit"
            
        }).then((result) => {
            if (result.isConfirmed) {
                // Ajax start
                $.ajax({

                    url: '/createPartFourRecord',
                    type: 'POST',
                    data: uploadedData,
                    contentType: false,
                    processData: false,
                    dataType: 'JSON',

                    success: function (data) {
                        if(data.status){
                            AddRowPartFour(data.insertedId);
                            // Reset the text color of the panel title
                            panelTitle.style.color = '#021691';

                            // Loop through the elements and update the data-toggle attribute
                            for (var i = 0; i < toggleLinks.length; i++) {
                                // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                                toggleLinks[i].setAttribute('data-toggle', 'noName');
                            }

                            // Enable the submit and save draft button
                            submitButton.disabled = false;
                            saveDraftButton.disabled = false;

                            //  If successful, Enable the Add button
                            cat4AddBtn.disabled = false;

                            // Redirect to the application.store route
                            window.location.href = "<?php echo e(route('application.store')); ?>";
                        }

                    },
                    error: function(xhr, status, error) {
                        alert("Somethig went worng");
                    }

                    });
                // Ajax end
                    Swal.fire({
                        title: "Success!",
                        // text: "Your application has been submitted successfuly.",
                        icon: "success"
                    });
            }
        });
            
        }

    }

// Validate all input are empty or not
function validateCategory04() {
    // Get values from form
    const academicWork = document.querySelector('#academicWorkSelect option:checked').text;
    const datePublished = document.getElementById("inputCategory4Date").value;
    const details = document.getElementById("inputCategory4Details").value;
    const cat4File = document.getElementById('cat4UploadFile').value;
    // get values for value is set or not
    const panelTitle = document.getElementById('cat4PanelTitle');
    const submitButton = document.getElementById('finalSubmitBtn');
    const saveDraftButton = document.getElementById('finalSaveDraftBtn');
    // Get elements by class name
    var toggleLinks = document.getElementsByClassName('toggleLinks');

        //Validate Catgory 4 Academic Work selection error msg
        var isValidateCat3 = true;
        if(academicWork =="Academic Work"){
            isValidateCat3 = false;
            document.getElementById("cat4AcademicWorkErrorMsg").style.display = "block";
            // document.getElementById("titleErrorMsg").style.color = "red";
        }else{
            document.getElementById("cat4AcademicWorkErrorMsg").style.display = "none";
        }

        //Validate Catagory 4 Published date error msg
        if(datePublished ==""){
            isValidateCat3 = false;
            document.getElementById("cat4PublishedDateErrorMsg").style.display = "block";
        }else{
            document.getElementById("cat4PublishedDateErrorMsg").style.display = "none";
        }

        //Validate Catagory 4 check Details error msg
        if(details ==""){
            isValidateCat3 = false;
            document.getElementById("cat4DetailsErrorMsg").style.display = "block";
        }else{
            document.getElementById("cat4DetailsErrorMsg").style.display = "none";
        }

        //Validate Catagory 4 check file upload error msg
        if(cat4File ==""){
            isValidateCat3 = false;
            document.getElementById("cat4FileUploadErrorMsg").style.display = "block";
        }else{
            document.getElementById("cat4FileUploadErrorMsg").style.display = "none";
        }

        // check for the value is added or not
        if(academicWork != "Academic Work" || datePublished != ""){
                // Change the text color of the panel title
                panelTitle.style.color = '#f2820a';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Disable the submit and save draft button
                submitButton.disabled = true;
                saveDraftButton.disabled = true;
            }else{
                // Reset the text color of the panel title
                panelTitle.style.color = '#021691';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Enable the submit button
                submitButton.disabled = false;
                saveDraftButton.disabled = false;
            }

        return isValidateCat3;

    }
    //Validata upload only .pdf for Category Four
    function validatePDFCategory04() {
        // Get the file input element
        var fileInput = document.getElementById('cat4UploadFile');

        // Get the selected file
        var selectedFile = fileInput.files[0];

        // Maximum file size in bytes (35MB)
        var maxSize = 35 * 1024 * 1024;

        // Check if a file is selected
        if (selectedFile) {
            // Get the file extension
            var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

            // Check if the file extension is not 'pdf'
            if (fileExtension !== 'pdf') {
                // Display error message for incorrect file type
                document.getElementById('cat4FileUploadErrorMsg').innerText = '*Please upload a .pdf file.*';
                document.getElementById('cat4FileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            } 
            // Check if file size exceeds 35MB
            else if (selectedFile.size > maxSize) {
                // Display error message for file size exceeding 35MB
                document.getElementById('cat4FileUploadErrorMsg').innerText = '*File size should not exceed 35MB.*';
                document.getElementById('cat4FileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            } 
            else {
                // Hide error message
                document.getElementById('cat4FileUploadErrorMsg').style.display = 'none';
            }
        }
    }

    // Category 4 Clear button
    function cat4Clear(){
            document.getElementById("academicWorkSelect").value = 'Academic Work';
            document.getElementById("inputCategory4Date").value = '';
            document.getElementById("inputCategory4Details").value = '';
            document.getElementById('cat4UploadFile').value = '';
            // Clear error msg
            document.getElementById("cat4AcademicWorkErrorMsg").style.display = "none";
            document.getElementById("cat4PublishedDateErrorMsg").style.display = "none";
            document.getElementById("cat4DetailsErrorMsg").style.display = "none";
            document.getElementById("cat4FileUploadErrorMsg").style.display = "none";

            //  When it clicks the clear button, Enable the Add button
            var cat4AddBtn = document.getElementById('categoryFourAddBtn');
            cat4AddBtn.disabled = false;

            var panelTitleID = 'cat4PanelTitle';
            resetAllPanel(panelTitleID);

        }
</script>
<?php /**PATH D:\Development\research_allowness\resources\views/frontend/application/component/category04.blade.php ENDPATH**/ ?>