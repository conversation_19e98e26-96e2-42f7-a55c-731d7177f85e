<div class="panel panel-default">
    <a class="toggleLinks" data-toggle="collapse" data-parent="#accordion" href="#collapse7">
        <div class="panel-heading" id="cat7Panel">
            <h5 class="panel-title" id="cat7PanelTitle">
            Category 7: Research Proposals
            </h5>
            
        </div>
    </a>
<div id="collapse7" class="panel-collapse collapse">
    <div class="panel-body">
    <!-- *******************Category seven form start****************** -->
    <form id="cat7Form" style="display: <?php echo $partSevenDataProposal !== null ? "none" : "block"; ?>">
        <!-- 7.1 -->
        <fieldset class="form-group">
            <div class="row">
                <!-- 7.1 -->
                <!-- <label for="lable" class="col-sm-10 col-form-label">Are you on study leave or sabbatical leave and engaged in reseach work or
                    any work as mentioned in 1 to 7, locally or abroad?
                </label>
                <div class="col-sm-2">
                    <div class="form-check">
                        Yes
                        <input class="form-check-input" type="radio" name="onStudyOrSabbaticalLeave" id="onStudyOrSabbaticalLeave" value="Yes">
                        <label class="form-check-label" for="gridRadios1">
                            Yes
                        </label>
                        No
                        <input class="form-check-input" type="radio" name="onStudyOrSabbaticalLeave" id="onStudyOrSabbaticalLeave" value="No">
                        <label class="form-check-label" for="gridRadios2">
                            No
                        </label>
                    </div>
                </div> -->
                <!-- 7.2 -->
                <!-- <label for="lable" class="col-sm-10 col-form-label">Have you submitted a research proposal to abtain the research allowance in <?php echo $currentYear-2?>
                    or <php echo $currentYear-1?>?
                </label>
                <div class="col-sm-2">
                    <div class="form-check">
                        Yes
                        <input class="form-check-input" type="radio" name="researchProposalForAllowance" id="researchProposalForAllowance" value="Yes">
                        <label class="form-check-label" for="gridRadios1">
                            Yes
                        </label>
                        No
                        <input class="form-check-input" type="radio" name="researchProposalForAllowance" id="researchProposalForAllowance" value="No">
                        <label class="form-check-label" for="gridRadios2">
                            No
                        </label>
                    </div>
                </div> -->
            </div>
        </fieldset>
        <!-- Title -->
        <div class="form-group row" >
            <label for="lableProposalTitle" class="col-sm-2 col-form-label">Title of the research <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="text" class="form-control" name="titleOfResearchProposal" id="titleOfResearchProposal" placeholder="Title of the research proposal" oninput="validateCategory07()">
                <p id="cat7TitleErrorMsg" style="display:none" class="allInputErrorMsg"> *Please Enter Title of the research proposal* </p>
            </div>
        </div>
        <!-- 7.3 -->
        <!-- <fieldset class="form-group">
            <div class="row">
                <label for="lable" class="col-sm-10 col-form-label">Have you published the research findings based on your <php echo $currentYear-2?>/<?php echo $currentYear-1?> proposal? <br>
                (If you have published, please submit the relevant publication with this application.)
                </label>
                <div class="col-sm-2">
                    <div class="form-check">
                        Yes
                        <input class="form-check-input" type="radio" name="publishedFindingsResearchProposal" id="publishedFindingsResearchProposal" value="Yes">
                        <label class="form-check-label" for="gridRadios1">
                            Yes
                        </label>
                        No
                        <input class="form-check-input" type="radio" name="publishedFindingsResearchProposal" id="publishedFindingsResearchProposal" value="No">
                        <label class="form-check-label" for="gridRadios2">
                            No
                        </label>
                    </div>
                </div>
            </div>
        </fieldset> -->
        <!-- 7.4 -->
        <div class="form-group row">
            <label for="lable" class="col-sm-2 col-form-label">Upload the proposal <span class="asterisk">*</span></label>
            <div class="col-sm-8">
                <input type="file" class="form-control" name="cat7File" id="cat7FileUpload" oninput="validatePDFCategory07()" accept=".pdf">
                <p id="cat7FileUploadErrorMsg" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
            </div>
            <!-- Category seven Add button -->
            <div class="col-sm-3 col-md-1">
                <button type="button" name="categorySevenAdd" id="categorySevenAddBtn" class="btn btn-primary add-btn" onclick="AddPartSevenRecoredToDb()">Submit</button><!--disabled-->
            </div>
            <!-- Category Seven Clear button -->
            <div class="col-sm-3 col-md-1">
                <button type="button" class="btn btn-warning clear-btn" onclick="cat7Clear()">Clear</button>
            </div>
        </div>
        <!-- Category 7 URL -->
        <div class="form-group row" style="display: none;">
            <label for="inputURL" id="cat7UrlLable" class="col-sm-2 col-form-label">Relevant URL <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="url" name="relevantURL" class="form-control" oninput="validateCategory07()" id="cat7InputRelevantURL" placeholder="Relevant URL">
                <p id="cat7URLErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter Relevant URL or upload a .pdf file* </p>
            </div>
        </div>
        <!-- <p>(if the given reason is accepted by the Research Council your will be eligible to receive the allowance,
            from January <php echo $currentYear?> to December <php echo $currentYear?>.)</p> -->
        <p id="cat7AllRequiredErrorMsg" style="display:none" class="allInputErrorMsg"> *You have not filled some fields. Check again* </p>

    </form>
    <!-- *******************form end******************** -->
    <!-- Table 7 start-->
    <div class="form-group row" style="overflow-x:auto; display:none;">
        <table id="categorySevenTable" class="table table-bordered">
            <thead>
                <tr>
                    <th scope="col">No</th>
                    <th scope="col">Title of Research Proposal</th>
                    <th scope="col">File</th>
                    <th scope="col">Action</th>
                </tr>
                </thead>
                <tbody>
                    <?php $partSevenrowcount = 0; ?>
                        <?php if($partSevenDataProposal !== null): ?>
                            <?php $__currentLoopData = $partSevenDataProposal; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $partSevenrowcount = ++$partSevenrowcount; ?>
                                <tr>
                                    <td><?php echo e($partSevenrowcount); ?></td>
                                    <td><?php echo e($row->titleOfResearchProposal); ?></td>
                                    <td>
                                        <?php if($row->uploadFile): ?>
                                            <span class="badge badge-pill badge-success">Uploaded</span>
                                            <?php else: ?>
                                            <span class="badge badge-pill badge-dark">No Attachment</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><button class="btn btn-sm btn-danger" onclick="deleteDBrowPart7('<?php echo e($row->id); ?>','<?php echo e($partSevenrowcount); ?>','1')">Delete</button></td>
                                </tr>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <?php endif; ?>
                    <!-- data will be add -->
                </tbody>
        </table>
    </div>
    <!-- Table 7 end -->
    </div>
  </div>
</div>

<!-- Catagory 7 lower table -->
<script>
    function categorySevenAddRow(insertedId) {
        // Validate category 7 and add new row
        // Get the radio button values
        //const onStudyOrSabbaticalLeave = document.querySelector('input[name="onStudyOrSabbaticalLeave"]:checked') ? document.querySelector('input[name="onStudyOrSabbaticalLeave"]:checked').value : '';
        //const researchProposalForAllowance = document.querySelector('input[name="researchProposalForAllowance"]:checked') ? document.querySelector('input[name="researchProposalForAllowance"]:checked').value : '';
        const titleOfResearchProposal = document.getElementById('titleOfResearchProposal').value;
        //const publishedFindingsResearchProposal = document.querySelector('input[name="publishedFindingsResearchProposal"]:checked') ? document.querySelector('input[name="publishedFindingsResearchProposal"]:checked').value : '';
        const cat7File = document.getElementById('cat7FileUpload').value; // You may need to handle file uploads differently
        const cat7URL = document.getElementById('cat7InputRelevantURL').value;

        const panelTitle = document.getElementById('cat7PanelTitle');
        const submitButton = document.getElementById('finalSubmitBtn');
        const saveDraftButton = document.getElementById('finalSaveDraftBtn');
        // Get elements by class name
        var toggleLinks = document.getElementsByClassName('toggleLinks');


        var isAllValidate = 1;
        // Validate form values
        if (!titleOfResearchProposal || !cat7File && !cat7URL) {
            document.getElementById('cat7AllRequiredErrorMsg').style.display = 'block';
            isAllValidate = 0;
            // return;
        }else{
            document.getElementById('cat7AllRequiredErrorMsg').style.display = 'none';
            // hidden form 7
            document.getElementById('cat7Form').style.display = 'none';
            isAllValidate = 1;
        }

        // Get the table reference
        const table = document.getElementById('categorySevenTable').getElementsByTagName('tbody')[0];

        // Create a new row
        var newRow = table.insertRow();

         // Insert cells
        var cell1 = newRow.insertCell(0);
        var cell2 = newRow.insertCell(1);
        //var cell3 = newRow.insertCell(2);
        var cell4 = newRow.insertCell(2);
        var cell5 = newRow.insertCell(3);

        // Populate cells with form values
        cell1.innerHTML = table.rows.length; // Auto-increment No column
        cell2.innerHTML = titleOfResearchProposal;
        //cell3.innerHTML = '<a href="'+ cat7URL +'" target=”_blank”>'+cat7URL+'</a>';
        cell4.innerHTML = cat7File ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';
        cell5.innerHTML = '<span class="btn btn-sm btn-danger" onclick="deleteDBrowPart7('+ insertedId +',1,0)">Delete</span>';

        // Insert cells
        //const noCell = newRow.insertCell(0);
        // const leaveCell = newRow.insertCell(1);
        // const proposalCell = newRow.insertCell(2);
        //const titleCell = newRow.insertCell(1);
        // const findingsCell = newRow.insertCell(4);

        //const URLCell = newRow.insertCell(2);
        //const fileCell = newRow.insertCell(3);
        //const actionCell = newRow.insertCell(4);

        // Set the text of the cell elements
        //noCell.textContent = table.rows.length;
        // leaveCell.textContent = onStudyOrSabbaticalLeave;
        // proposalCell.textContent = researchProposalForAllowance;
        //titleCell.textContent = titleOfResearchProposal;
        // findingsCell.textContent = publishedFindingsResearchProposal;
        //URLCell.textContent = cat7URL;
        //fileCell.textContent = cat7File ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';

        //actionCell.innerHTML = '<span class="btn btn-sm btn-danger" onclick="deleteDBrowPart7('+insertedId+', 1,0)">Delete</span>';


        // Reset the text color of the panel title
        panelTitle.style.color = '#021691';

        // Loop through the elements and update the data-toggle attribute
        for (var i = 0; i < toggleLinks.length; i++) {
            // toggleLinks[i].setAttribute('data-toggle', 'collapse');
            toggleLinks[i].setAttribute('data-toggle', 'noName');
        }


        // Redirect to the application.store route
        window.location.href = "<?php echo e(route('application.store')); ?>";

        // Enable the submit button
        submitButton.disabled = false;
        saveDraftButton.disabled = false;

        // Clear form values
        clearCategorySevenForm();
        return isAllValidate;
    }

    function categorySevenDeleteRow(row,accNo) {


        categorySevenTable.deleteRow(1);
        // show Form 7 again
        document.getElementById('cat7Form').style.display = 'block';
        document.getElementById('titleOfResearchProposal').value = '';
        const cat7File = document.getElementById('cat7FileUpload').value = '';
        const cat7URL = document.getElementById('cat7InputRelevantURL').value = '';

        // collapse - enable
        var panelTitleID = 'cat7PanelTitle';
        resetAllPanel(panelTitleID);
    }

    // Function to clear form values
    function clearCategorySevenForm() {
        document.querySelector('input[name="onStudyOrSabbaticalLeave"]:checked').checked = false;
        document.querySelector('input[name="researchProposalForAllowance"]:checked').checked = false;
        document.getElementById('titleOfResearchProposal').value = '';
        document.querySelector('input[name="publishedFindingsResearchProposal"]:checked').checked = false;
        document.getElementById('cat7FileUpload').value = '';
    }

    // Category 7 add to DB *******************************
    // Function to delete a row from the table
    function cat7DeleteRow(row,accNo) {
        if(accNo == '1'){
            categorySevenTable.deleteRow(row);
        }else{
            categorySevenTable.deleteRow(row.rowIndex);
        }

    }

    // Delete in the row of database
    function deleteDBrowPart7(deletedId,tblRowId,accNo){
        //   Catagory Seven - Ajax request for delete row
        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
            $.ajax({

                url: '/deletePartSevenRecord',
                type: 'POST',
                data: {_token: CSRF_TOKEN,
                    deletedId: deletedId,
                },
                dataType: 'JSON',

                success: function (data) {
                    if(data.status){
                        categorySevenDeleteRow(tblRowId,accNo);
                    }else{
                        alert("Error");
                    }

                },
                error: function(xhr, status, error) {
                    alert("Somethig went worng");
                }

            });
    }
    function AddPartSevenRecoredToDb(){
        if(validateCategory07()){
            // Get values from form
            //const onStudyOrSabbaticalLeave = document.querySelector('input[name="onStudyOrSabbaticalLeave"]:checked') ? document.querySelector('input[name="onStudyOrSabbaticalLeave"]:checked').value : '';
           // const researchProposalForAllowance = document.querySelector('input[name="researchProposalForAllowance"]:checked') ? document.querySelector('input[name="researchProposalForAllowance"]:checked').value : '';
            const titleOfResearchProposal = document.getElementById('titleOfResearchProposal').value;
            //const publishedFindingsResearchProposal = document.querySelector('input[name="publishedFindingsResearchProposal"]:checked') ? document.querySelector('input[name="publishedFindingsResearchProposal"]:checked').value : '';
            const cat7RelevantURL = document.getElementById('cat7InputRelevantURL').value;
            const cat7File = document.getElementById('cat7FileUpload').value; // You may need to handle file uploads differently
            const cat7File1 = document.getElementById('cat7FileUpload');


            var files = $('#cat7FileUpload')[0].files;
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');

            var uploadedData = new FormData();
            uploadedData.append('titleOfResearchProposal',titleOfResearchProposal);
            uploadedData.append('cat7RelevantURL',cat7RelevantURL);
            uploadedData.append('_token',CSRF_TOKEN);
            uploadedData.append('file',files[0]);

            // Add button disable
            var cat7AddBtn = document.getElementById('categorySevenAddBtn');
            cat7AddBtn.disabled = true;

        //   Catagory Seven - Ajax request
        Swal.fire({
            title: "Are you sure that you want to submit?",
            text: "You will not be able to edit the application after submitting it",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes. Submit"

        }).then((result) => {
            if (result.isConfirmed) {
                // Ajax start
                    $.ajax({
                    url: '/createPartSevenRecord',
                    method: 'post',
                    data: uploadedData,
                    contentType: false,
                    processData: false,
                    dataType: 'json',
                    success: function(data) {
                        if(data.status){
                            categorySevenAddRow(data.insertedId);

                            //  If successful, Enable the Add button
                            cat7AddBtn.disabled = false;
                        }

                        },
                        error: function(error) {
                            alert("somethig went worng")
                        }
                    });
                // Ajax end
                    Swal.fire({
                        title: "Success!",
                        // text: "Your application has been submitted successfuly.",
                        icon: "success"
                    });
            }
        });


        }

    }

    // category 7 validation
    function validateCategory07(){
        // Get the radio button values
        //const onStudyOrSabbaticalLeave = document.querySelector('input[name="onStudyOrSabbaticalLeave"]:checked') ? document.querySelector('input[name="onStudyOrSabbaticalLeave"]:checked').value : '';
        //const researchProposalForAllowance = document.querySelector('input[name="researchProposalForAllowance"]:checked') ? document.querySelector('input[name="researchProposalForAllowance"]:checked').value : '';
        const titleOfResearchProposal = document.getElementById('titleOfResearchProposal').value;
        const cat7URL = document.getElementById('cat7InputRelevantURL').value;
        //const publishedFindingsResearchProposal = document.querySelector('input[name="publishedFindingsResearchProposal"]:checked') ? document.querySelector('input[name="publishedFindingsResearchProposal"]:checked').value : '';
        const cat7File = document.getElementById('cat7FileUpload').value; // You may need to handle file uploads differently
        // Get values for panel disable
        const panelTitle = document.getElementById('cat7PanelTitle');
        const submitButton = document.getElementById('finalSubmitBtn');
        const saveDraftButton = document.getElementById('finalSaveDraftBtn');
        // Get elements by class name
        var toggleLinks = document.getElementsByClassName('toggleLinks');

        // Validate title Of Research Proposal values
        var isValidateCat7 = true;
        if (titleOfResearchProposal == '') {
            document.getElementById('cat7TitleErrorMsg').style.display = 'block';
            isValidateCat7 = false;
        }else{
            document.getElementById('cat7TitleErrorMsg').style.display = 'none';
            // document.getElementById('cat7Form').style.display = 'none';
        }

        // Validate category 7 URL and File upload
        if (cat7URL == '' && cat7File == '') {
            document.getElementById('cat7URLErrorMsg').style.display = 'block';
            document.getElementById('cat7FileUploadErrorMsg').style.display = 'block';
            isValidateCat7 = false;
        }else{
            document.getElementById('cat7URLErrorMsg').style.display = 'none';
            document.getElementById('cat7FileUploadErrorMsg').style.display = 'none';
        }

        // check for the value is added or not
        if(titleOfResearchProposal != ''){
                // Change the text color of the panel title
                panelTitle.style.color = '#f2820a';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Disable the submit and save draft button
                submitButton.disabled = true;
                saveDraftButton.disabled = true;
            }else{
                // Reset the text color of the panel title
                panelTitle.style.color = '#021691';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Enable the submit button
                submitButton.disabled = false;
                saveDraftButton.disabled = false;
            }


        return isValidateCat7;
    }
    //Validata upload only .pdf for Category Seven
    function validatePDFCategory07() {
        // Get the file input element
        var fileInput = document.getElementById('cat7FileUpload');

        // Get the selected file
        var selectedFile = fileInput.files[0];

        // Maximum file size in bytes (35MB)
        var maxSize = 35 * 1024 * 1024;

        // Check if a file is selected
        if (selectedFile) {
            // Get the file extension
            var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

            // Check if the file extension is not 'pdf'
            if (fileExtension !== 'pdf') {
                // Display error message for incorrect file type
                document.getElementById('cat7FileUploadErrorMsg').innerText = '*Please upload a .pdf file.*';
                document.getElementById('cat7FileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            }
            // Check if file size exceeds 35MB
            else if (selectedFile.size > maxSize) {
                // Display error message for file size exceeding 35MB
                document.getElementById('cat7FileUploadErrorMsg').innerText = '*File size should not exceed 35MB.*';
                document.getElementById('cat7FileUploadErrorMsg').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            }
            else {
                // Hide error message
                document.getElementById('cat7FileUploadErrorMsg').style.display = 'none';
            }
        }
    }

    // Category 7 Clear button
    function cat7Clear(){
            document.getElementById('titleOfResearchProposal').value = '';
            document.getElementById('cat7FileUpload').value = '';
            document.getElementById('cat7InputRelevantURL').value = '';
            // Clear error msg
            document.getElementById("cat7TitleErrorMsg").style.display = "none";
            document.getElementById("cat7FileUploadErrorMsg").style.display = "none";
            document.getElementById("cat7URLErrorMsg").style.display = "none";

            //  When it clicks the clear button, Enable the Add button
            var cat7AddBtn = document.getElementById('categorySevenAddBtn');
            cat7AddBtn.disabled = false;

            var panelTitleID = 'cat7PanelTitle';
            resetAllPanel(panelTitleID);

        }
</script>
<!-- can't apply after 31st of march -->
<script>
    document.addEventListener("DOMContentLoaded", function() {
    // Get the elements
    const message = document.getElementById('message');
    const cat7Panel = document.getElementById('cat7Panel');

    // Add click event listener to the button
    cat7Panel.addEventListener('click', function() {
        // Show the message
        message.style.display = 'block';

        // After 3 seconds, hide the message
        setTimeout(function() {
            message.style.display = 'none';
        }, 3000); // 3000 milliseconds = 3 seconds
    });
});
</script>
<?php /**PATH D:\Development\research_allowness\resources\views/frontend/application/component/category07.blade.php ENDPATH**/ ?>