<div class="panel panel-default">
    <a class="toggleLinks" data-toggle="collapse" data-parent="#accordion" href="#collapse2">
        <div class="panel-heading">
            <h5 class="panel-title" id="cat2PanelTitle">
            Category 2: Other Publications
            </h5>
        </div>
    </a>
<div id="collapse2" class="panel-collapse collapse">
    <div class="panel-body">
        <!-- *******************Category two form start****************** -->
        <form style="display: <?php echo $partTwoData !== null ? "none" : "block" ?>;" id="cat2form">
        <!-- 2.1 -->
        <fieldset class="form-group">
        <?php echo csrf_field(); ?>
            <div class="row">
                <label for="lable" class="col-sm-2 col-form-label">Publication <span class="asterisk">*</span></label>
                <div class="col-sm-10">
                    <div class="form-check">
                    <input class="form-check-input" type="radio" name="publication" id="cat2GridRadios1" value="non-Indexed Journal Publication" checked>
                    <label class="form-check-label" for="cat2GridRadios1">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Non-Indexed Journal Publication
                    </label>
                    </div>
                    <div class="form-check">
                    <input class="form-check-input" type="radio" name="publication" id="cat2GridRadios4" value="Cabell's International">
                    <label class="form-check-label" for="cat2GridRadios4">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Cabell's International
                    </label>
                    </div>
                    <div class="form-check">
                    <input class="form-check-input" type="radio" name="publication" id="cat2GridRadios2" value="Symposium/ Conference/ Technical Session Paper, Abstract or Poster">
                    <label class="form-check-label" for="cat2GridRadios2">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Symposium/ Conference/ Technical Session Paper/ Abstract or Poster
                    </label>
                    </div>
                    <!-- <div class="form-check">
                    <input class="form-check-input" type="radio" name="publication" id="cat2GridRadios3" value="Conference">
                    <label class="form-check-label" for="cat2GridRadios3">
                    </label>
                    </div> -->
                </div>
            </div>
        </fieldset>
        <!-- 2.2 -->
        <div class="form-group row">
            <label for="inputTitle" class="col-sm-2 col-form-label">Title <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="text" name="title" class="form-control" oninput="validateCategory02()" id="cat2InputTitle" placeholder="Title" required>
                <p id="cat2TitleErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter the title* </p>
            </div>
        </div>
        <!-- 2.3 -->
        <div class="form-group row">
            <label for="inputJournalName" class="col-sm-2 col-form-label">Name of the Journal/ Symposium/ Conference/ Technical Session <span class="asterisk">*</span></label>
            <div class="col-sm-10">
                <input type="text" name="sessionName" class="form-control" oninput="validateCategory02()" id="cat2InputJournalName" placeholder="Journal/ Symposium/ Conference/ Technical Session Name" required>
                <p id="cat2JournalNameErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter Journal/ Symposium/ Conference/ Technical Session Name* </p>
            </div>
        </div>
        <!-- 2.4  Acceptance-->
        <div class="form-group row">
            <label for="checkPublication" class="col-sm-2 col-form-label">Status <span class="asterisk">*</span></label>
            <div class="col-sm-2">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="cat2Status" id="cat2CheckAccepted2" value="1">
                <label class="form-check-label" for="gridCheck1">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Accepted
                </label>
            </div>
            </div>
            <!-- 2.5 Acceptance -->
            <div class="col-sm-2">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="cat2Status" id="cat2CheckPublication2" value="2">
                <label class="form-check-label" for="gridCheck1">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Published
                </label>
            </div>
            </div>
            <p id="cat2AtLeastOne" style="display:none" class="allInputErrorMsg"> *You must select at least one before adding* </p>
        </div>
        <!-- 2.5 Acceptance -->
        <div class="form-group row" style="margin-bottom: 0px;">
            <label for="inputAcceptDate" class="col-sm-2 col-form-label"></label>
            <label for="inputAcceptDate" class="col-sm-2 col-form-label" id="cat2AcceptDateLable2">Date of Acceptance <span class="asterisk">*</span></label>
            <div class="col-sm-8">
                <input type="date" name="dateOfAcceptance" class="form-control" oninput="validateCategory02()" id="cat2InputAcceptDate2">
                <p id="cat2AcceptDateErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter Date of Acceptance* </p>
            </div>
        </div>
        <!-- 2.4 Publication -->
        <!-- <div class="form-group row">
            <label for="checkPublication" class="col-sm-2 col-form-label"></label>
            <div class="col-sm-10">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="cat2Status" id="cat2CheckPublication2">
                <label class="form-check-label" for="gridCheck1">
                Published
                </label>
            </div>
            </div>
        </div> -->
        <!-- 2.6 Publication -->
        <div class="form-group row" style="margin-bottom: 0px;">
            <label for="inputPublication" class="col-sm-2 col-form-label"></label>
            <label for="inputPublication" class="col-sm-2 col-form-label" id="cat2PublicationLable2">Date of Publication/ Presentation <span class="asterisk">*</span></label>
            <div class="col-sm-8">
                <input type="date" name="dateOfPublication" class="form-control" oninput="validateCategory02()" id="cat2InputPublication2">
                <p id="cat2PublicationInfoErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter Date of Publication/ Presentation* </p>
            </div>
        </div>
        <!-- 2.7 -->
        <div class="form-group row" style="margin-bottom: 15px;">
            <label for="inputURL" class="col-sm-2 col-form-label"></label>
            <label for="inputURL" id="cat2UrlLable" class="col-sm-2 col-form-label">Relevant URL</label>
            <div class="col-sm-8">
                <input type="url" name="relevantURL" class="form-control" oninput="validateCategory02()" id="cat2InputRelevantURL" placeholder="Relevant URL">
                <p id="cat2URLErrorMsg" style="display:none" class="allInputErrorMsg"> *Please enter the Relevant URL* </p>
            </div>
        </div>
        <!-- 2.8 -->
        <div class="form-group row">
            <label for="inputURL" class="col-sm-2 col-form-label"></label>
            <label for="inputFile" id="cat2fileUploadLable" class="col-sm-2 col-form-label">File (Upload) <span class="asterisk">*</span></label>
            <div class="col-sm-8">
                <input type="file" name="uploadFile" class="form-control" oninput="validatePDFCategory02()" id="cat2UploadFile" accept=".pdf">
                <p id="cat2FileUploadErrorMsg" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
            </div>
        </div>

        <!-- Category two draft button -->
        <div class="form-group row">
            <div class="col-sm-6 col-md-10">
                <!-- <button type="submit" name="categoryTwoSubmit" class="btn btn-primary">Save Draft</button> -->
            </div>
            <!-- Category two add button -->
            <div class="col-sm-3 col-md-1">
                <button type="button" onclick="AddPartTwoRecoredToDb()" id="categoryTwoAddBtn" name="categoryTwoAdd" class="btn btn-primary add-btn">Submit</button>
            </div>
            <!-- Category Two Clear button -->
            <div class="col-sm-3 col-md-1">
                <button type="button" class="btn btn-warning clear-btn" onclick="cat2Clear()">Clear</button>
            </div>
        </div>
        </form>
        <!-- *******************form end******************** -->
        <!-- Table 2 start-->
        <div class="form-group row" style="overflow-x:auto; display:none;">
            <table id="catagoryTwoTable" class="table table-bordered">
                <thead>
                    <tr>
                        <th scope="col">No</th>
                        <th scope="col">Publication</th>
                        <th scope="col">Title</th>
                        <th scope="col">Name</th>
                        <th scope="col">Status</th>
                        <th scope="col">Date of Acceptence</th>
                        <th scope="col">Publication Summary</th>

                        <th scope="col">File</th>
                        <th scope="col">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                        <?php $partTworowcount = 0; ?>
                        <?php if($partTwoData !== null): ?>
                            <?php $__currentLoopData = $partTwoData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $partTworowcount = ++$partTworowcount; ?>
                                <tr id="parttwo<?php echo e($index+1); ?>">
                                    <td><?php echo e($index+1); ?></td>
                                    <td><?php echo e($row->publication); ?></td>
                                    <td><?php echo e($row->title); ?></td>
                                    <td><?php echo e($row->journalName); ?></td>
                                    <td>
                                        <?php if($row->status == 1): ?>
                                        Accepted
                                        <?php elseif($row->status == 2): ?>
                                        Published
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($row->dateOfAcceptance); ?></td>
                                    <td>
                                        <?php if($row->status == 2): ?>
                                        <b> Date of Publication: </b> <?php echo e($row->dateOfPublication); ?>

                                        <br><b>Relavent URL : </b><a href="<?php echo e($row->relevantURL); ?>" target=”_blank”><?php echo e($row->relevantURL); ?></a>
                                        <?php endif; ?>
                                    </td>

                                    <td>
                                        <?php if($row->uploadFile): ?>
                                        <span class="badge badge-pill badge-success">Uploaded</span>
                                        <?php else: ?>
                                        <span class="badge badge-pill badge-dark">No Attachment</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><button class="btn btn-sm btn-danger" onclick="deleteDBrowPart2('<?php echo e($row->id); ?>','<?php echo e($index+1); ?>','1')">Delete</button></td>
                                </tr>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <?php endif; ?>

                        <!-- data will be add -->
                    </tbody>
            </table>
        </div>
        <!-- Table 2 end -->
    </div>
  </div>
</div>


<!-- Category two 2.5 acceptance -->
<script>

    // Get the checkbox and input field elements
    const checkbox2 = document.getElementById('cat2CheckAccepted2');
    const inputField2 = document.getElementById('cat2InputAcceptDate2');
    const acceptLable2 = document.getElementById('cat2AcceptDateLable2');
    const publicationCheckbox2 = document.getElementById('cat2CheckPublication2');
    const publicationInputField2 = document.getElementById('cat2InputPublication2');
    const publicationLable2 = document.getElementById('cat2PublicationLable2');
    const urlLable = document.getElementById('cat2UrlLable');
    const relevantURL = document.getElementById('cat2InputRelevantURL');
    const cat2File = document.getElementById('cat2UploadFile');
    const cat2fileUploadLable = document.getElementById('cat2fileUploadLable');

    // Add a change event listener to the checkbox
    checkbox2.addEventListener('change', function() {

        // If the checkbox is checked, make the input field visible
        if (checkbox2.checked) {
            inputField2.style.display = 'block';
            acceptLable2.style.display = 'block';
            cat2File.style.display = 'block';
            cat2fileUploadLable.style.display = 'block';
            cat2fileUploadLable.innerHTML = 'Upload the Acceptance Letter <span class="asterisk">*</span>';

            // Otherwise, hide the input field
            publicationInputField2.style.display = 'none';
            publicationLable2.style.display = 'none';
            urlLable.style.display = 'none';
            relevantURL.style.display = 'none';

            //Clear hidden items
            publicationInputField2.value = '';
            relevantURL.value = '';
            cat2File.value = '';
            // Clear error msg
            document.getElementById("cat2PublicationInfoErrorMsg").style.display = "none";
            document.getElementById("cat2URLErrorMsg").style.display = "none";
        } else {

        }
    });

    // Hide the input field by default

    // Category two 2.6 publication
    // Get the checkbox and input field elements

    // Add a change event listener to the checkbox
    publicationCheckbox2.addEventListener('change', function() {

        // If the checkbox is checked, make the input field visible
        if (publicationCheckbox2.checked) {
            publicationInputField2.style.display = 'block';
            publicationLable2.style.display = 'block';
            urlLable.style.display = 'block';
            relevantURL.style.display = 'block';
            cat2File.style.display = 'block';
            cat2fileUploadLable.style.display = 'block';
            cat2fileUploadLable.innerHTML = 'Upload the relevant pages';

            // Otherwise, hide the input field
            inputField2.style.display = 'none';
            acceptLable2.style.display = 'none';

            //Clear hidden items
            inputField2.value = '';
            cat2File.value = '';

            // Clear error msg
            document.getElementById("cat2AcceptDateErrorMsg").style.display = "none";
        } else {

        }
    });

    // Hide the input field by default
    publicationInputField2.style.display = 'none';
    publicationLable2.style.display = 'none';
    urlLable.style.display = 'none';
    relevantURL.style.display = 'none';
    inputField2.style.display = 'none';
    acceptLable2.style.display = 'none';
    cat2File.style.display = 'none';
    cat2fileUploadLable.style.display = 'none';

</script>

<!-- Category two lower table  -->
<script>

    let cat2RowCount = 0;
    var partTwoCount = '<?php echo e($partTwoCount); ?>';
    function AddRowPartTwo(insertedId){

            // Get values from form
            const publicationType = document.querySelector('input[name="publication"]:checked').value;
            const title = document.getElementById("cat2InputTitle").value;
            const journalSymposiumName = document.getElementById("cat2InputJournalName").value;
            const cat2Status = document.querySelector('input[name="cat2Status"]:checked').value;
            const isAccepted = document.getElementById("cat2CheckAccepted2").checked;
            const acceptedDate = isAccepted ? document.getElementById("cat2InputAcceptDate2").value : "";
            const isPublished = document.getElementById("cat2CheckPublication2").checked;
            const publicationDate = isPublished ? document.getElementById("cat2InputPublication2").value : "";
            const url = isPublished ?document.getElementById("cat2InputRelevantURL").value: "";
            const file = document.getElementById("cat2UploadFile").value;
            cat2RowCount++;
            partTwoCount++;

                if(validateCategory02()){
                    // Create a new row
                    const newRow2 = catagoryTwoTable.insertRow();

                    // Add data to the row
                    newRow2.insertCell(0).innerHTML = partTwoCount;
                    newRow2.insertCell(1).innerHTML = publicationType;
                    newRow2.insertCell(2).innerHTML = title;
                    newRow2.insertCell(3).innerHTML = journalSymposiumName;
                    newRow2.insertCell(4).innerHTML = cat2Status == 1 ? 'Accepted' : 'Published';
                    newRow2.insertCell(5).innerHTML = acceptedDate;
                    if(cat2Status == 2)
                    newRow2.insertCell(6).innerHTML = '<b> Date of Publication: </b>'+ publicationDate + '<br><b> Relavent URL : </b><a href="'+ url +'" target=”_blank”>'+url+'</a>' ;
                    else{
                    newRow2.insertCell(6).innerHTML = '';
                    }
                    newRow2.insertCell(7).innerHTML = file ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';


                    // Add delete button to the row with Bootstrap 'btn-danger' class
                    const deleteButtonCell = newRow2.insertCell(8);
                    const deleteButton = document.createElement("button");
                    deleteButton.textContent = "Delete";
                    deleteButton.className = "btn btn-sm btn-danger";

                    deleteButton.addEventListener("click", function () {;
                        deleteDBrowPart2(insertedId,newRow2,0);
                    });
                    deleteButtonCell.appendChild(deleteButton);

                    // Clear form fields
                    document.getElementById("cat2InputTitle").value = "";
                    document.getElementById("cat2InputJournalName").value = "";
                    document.getElementById("cat2CheckAccepted2").checked = false;
                    document.getElementById("cat2InputAcceptDate2").value = "";
                    document.getElementById("cat2CheckPublication2").checked = false;
                    document.getElementById("cat2InputPublication2").value = "";
                    document.getElementById("cat2InputRelevantURL").value = "";
                    document.getElementById("cat2UploadFile").value = "";
                    inputField2.style.display = 'none';
                    acceptLable2.style.display = 'none';
                    publicationInputField2.style.display = 'none';
                    publicationLable2.style.display = 'none';
                    urlLable.style.display = 'none';
                    relevantURL.style.display = 'none';
                    cat2File.style.display = 'none';
                    cat2fileUploadLable.style.display = 'none';
                    document.getElementById('cat2form').style.display = 'none';
                }

        }

        // Function to delete a row from the table
        function cat2DeleteRow(row,accNo) {
            if(accNo == '1'){
                var rowTemp = document.getElementById("parttwo"+row);
                rowTemp.parentNode.removeChild(rowTemp);
            }else{
                catagoryTwoTable.deleteRow(row.rowIndex);
            }

            document.getElementById('cat2form').style.display = 'block';

            // collapse - enable
            var panelTitleID = 'cat2PanelTitle';
            resetAllPanel(panelTitleID);
        }

        // Delete in the row of database
        function deleteDBrowPart2(deletedId,tblRowId,accNo){
            //   Catagory Two - Ajax request for delete row
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
                $.ajax({

                    url: '/deletePartTwoRecord',
                    type: 'POST',
                    data: {_token: CSRF_TOKEN,
                        deletedId: deletedId,
                    },
                    dataType: 'JSON',

                    success: function (data) {
                        if(data.status){
                            cat2DeleteRow(tblRowId,accNo);
                        }else{
                            alert("Error");
                        }

                    },
                    error: function(xhr, status, error) {
                        alert("Somethig went worng");
                    }

                });
        }
        function AddPartTwoRecoredToDb(){
            if(validateCategory02()){
                const publicationType = document.querySelector('input[name="publication"]:checked').value;
                const title = document.getElementById("cat2InputTitle").value;
                const journalSymposiumName = document.getElementById("cat2InputJournalName").value;
                const cat2Status = document.querySelector('input[name="cat2Status"]:checked').value;
                const isAccepted = document.getElementById("cat2CheckAccepted2").checked;
                const acceptedDate = isAccepted ? document.getElementById("cat2InputAcceptDate2").value : "";
                const isPublished = document.getElementById("cat2CheckPublication2").checked;
                const publicationDate = isPublished ? document.getElementById("cat2InputPublication2").value : "";
                const url = isPublished ?document.getElementById("cat2InputRelevantURL").value: "";
                const file = isAccepted ?document.getElementById("cat2UploadFile").value: "";
                // get values for value is set or not
                const panelTitle = document.getElementById('cat2PanelTitle');
                const submitButton = document.getElementById('finalSubmitBtn');
                const saveDraftButton = document.getElementById('finalSaveDraftBtn');
                // Get elements by class name
                var toggleLinks = document.getElementsByClassName('toggleLinks');

                // Add button disable
                var cat1AddBtn = document.getElementById('categoryTwoAddBtn');
                // cat1AddBtn.disabled = true;

                var files = $('#cat2UploadFile')[0].files;
                var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');

                var uploadedData = new FormData();
                uploadedData.append('publication',publicationType);
                uploadedData.append('title',title);
                uploadedData.append('journalName',journalSymposiumName);
                uploadedData.append('status',cat2Status);
                uploadedData.append('dateOfAcceptance',acceptedDate);
                uploadedData.append('dateOfPublication',publicationDate);
                uploadedData.append('relevantURL',url);
                uploadedData.append('_token',CSRF_TOKEN);
                uploadedData.append('file',files[0]);

            //   Catagory Two - Ajax request
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
                Swal.fire({
                    title: "Are you sure that you want to submit?",
                    text: "You will not be able to edit the application after submitting it",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes. Submit"
                
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Ajax start
                        $.ajax({

                            url: '/createPartTwoRecord',
                            type: 'POST',
                            data: uploadedData,
                            contentType: false,
                            processData: false,
                            dataType: 'JSON',

                            success: function (data) {
                                if(data.status){
                                    AddRowPartTwo(data.insertedId);
                                    // Reset the text color of the panel title
                                    panelTitle.style.color = '#021691';

                                    // Loop through the elements and update the data-toggle attribute
                                    for (var i = 0; i < toggleLinks.length; i++) {
                                        // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                                        toggleLinks[i].setAttribute('data-toggle', 'noName');
                                    }

                                    // Enable the submit and save draft button
                                    submitButton.disabled = false;
                                    saveDraftButton.disabled = false;

                                    //  If successful, Enable the Add button
                                    cat1AddBtn.disabled = false;

                                    // Redirect to the application.store route
                                    window.location.href = "<?php echo e(route('application.store')); ?>";
                                }

                            },
                            error: function(xhr, status, error) {
                                alert("Somethig went worng");
                            }

                            });
                        // Ajax end
                            Swal.fire({
                                title: "Success!",
                                // text: "Your application has been submitted successfuly.",
                                icon: "success"
                        });
                    }
                });
                
            }

        }

        // Validate all input are empty or not
        function validateCategory02() {
            // Get values from form
            const publicationType = document.querySelector('input[name="publication"]:checked').value;
            const title = document.getElementById("cat2InputTitle").value;
            const journalSymposiumName = document.getElementById("cat2InputJournalName").value;
            const isAccepted = document.getElementById("cat2CheckAccepted2").checked;
            const acceptedDate = isAccepted ? document.getElementById("cat2InputAcceptDate2").value : "";
            const isPublished = document.getElementById("cat2CheckPublication2").checked;
            const publicationDate = isPublished ? document.getElementById("cat2InputPublication2").value : "";
            const url = isPublished ?document.getElementById("cat2InputRelevantURL").value: "";
            const file = document.getElementById("cat2UploadFile").value;
            // get values for value is set or not
            const panelTitle = document.getElementById('cat2PanelTitle');
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');
            // Get elements by class name
            var toggleLinks = document.getElementsByClassName('toggleLinks');

                //Validate Catgory 2 title
                var isValidateCat2 = true;
                if(title ==""){
                    isValidateCat2 = false;
                    document.getElementById("cat2TitleErrorMsg").style.display = "block";
                    // document.getElementById("titleErrorMsg").style.color = "red";
                }else{
                    document.getElementById("cat2TitleErrorMsg").style.display = "none";
                }

                //Validate Catagory 2 journal Name
                if(journalSymposiumName ==""){
                    isValidateCat2 = false;
                    document.getElementById("cat2JournalNameErrorMsg").style.display = "block";
                }else{
                    document.getElementById("cat2JournalNameErrorMsg").style.display = "none";
                }

                //Validate Catagory 2 check Accepted and show Acceptance date error msg
                if(cat2CheckAccepted2.checked){
                    if(acceptedDate ==""){
                        isValidateCat2 = false;
                        document.getElementById("cat2AcceptDateErrorMsg").style.display = "block";
                    }else{
                        document.getElementById("cat2AcceptDateErrorMsg").style.display = "none";
                    }
                }
                //Validate Catagory 2 check Accepted and show upload file error msg
                if(cat2CheckAccepted2.checked){
                    if(file == ""){
                        isValidateCat2 = false;
                        document.getElementById("cat2FileUploadErrorMsg").style.display = "block";
                    }else{
                        document.getElementById("cat2FileUploadErrorMsg").style.display = "none";
                    }
                }


                //Validate Catagory 2 check Published and show publication info error msg
                if(cat2CheckPublication2.checked){
                    if(publicationDate ==""){
                        isValidateCat2 = false;
                        document.getElementById("cat2PublicationInfoErrorMsg").style.display = "block";
                    }else{
                        document.getElementById("cat2PublicationInfoErrorMsg").style.display = "none";
                    }
                }
                //Validate Catagory 2 check Published and show Relevant URL error msg and show upload file error msg
                if(cat2CheckPublication2.checked){
                    if(url == "" && file == ""){
                        isValidateCat2 = false;
                        document.getElementById("cat2URLErrorMsg").style.display = "block";
                        document.getElementById("cat2FileUploadErrorMsg").style.display = "block";
                    }else{
                        document.getElementById("cat2URLErrorMsg").style.display = "none";
                        document.getElementById("cat2FileUploadErrorMsg").style.display = "none";
                    }
                }

                // Either accepted or publication has been selected
                if (cat2CheckPublication2.checked == false && cat2CheckAccepted2.checked == false) {
                    isValidateCat2 = false;
                    document.getElementById("cat2AtLeastOne").style.display = "block"
                } else {
                    document.getElementById("cat2AtLeastOne").style.display = "none"
                }

                // check for the value is added or not
                if(title != "" || journalSymposiumName != ""){
                    // Change the text color of the panel title
                    panelTitle.style.color = '#f2820a';

                    // Loop through the elements and update the data-toggle attribute
                    for (var i = 0; i < toggleLinks.length; i++) {
                        toggleLinks[i].setAttribute('data-toggle', 'noName');
                    }

                    // Disable the submit and save draft button
                    submitButton.disabled = true;
                    saveDraftButton.disabled = true;
                }else{
                    // Reset the text color of the panel title
                    panelTitle.style.color = '#021691';

                    // Loop through the elements and update the data-toggle attribute
                    for (var i = 0; i < toggleLinks.length; i++) {
                        // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                        toggleLinks[i].setAttribute('data-toggle', 'noName');
                    }

                    // Enable the submit button
                    submitButton.disabled = false;
                    saveDraftButton.disabled = false;
                }


                return isValidateCat2;

        }

        //Validata upload only .pdf for Category Two
        function validatePDFCategory02() {
            // Get the file input element
            var fileInput = document.getElementById('cat2UploadFile');

            // Get the selected file
            var selectedFile = fileInput.files[0];

            // Maximum file size in bytes (35MB)
            var maxSize = 35 * 1024 * 1024;

            // Check if a file is selected
            if (selectedFile) {
                // Get the file extension
                var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

                // Check if the file extension is not 'pdf'
                if (fileExtension !== 'pdf') {
                    // Display error message for incorrect file type
                    document.getElementById('cat2FileUploadErrorMsg').innerText = '*Please upload a .pdf file.*';
                    document.getElementById('cat2FileUploadErrorMsg').style.display = 'block';

                    // Clear the file input
                    fileInput.value = '';
                } 
                // Check if file size exceeds 35MB
                else if (selectedFile.size > maxSize) {
                    // Display error message for file size exceeding 35MB
                    document.getElementById('cat2FileUploadErrorMsg').innerText = '*File size should not exceed 35MB.*';
                    document.getElementById('cat2FileUploadErrorMsg').style.display = 'block';

                    // Clear the file input
                    fileInput.value = '';
                } 
                else {
                    // Hide error message
                    document.getElementById('cat2FileUploadErrorMsg').style.display = 'none';
                }
            }
        }

        // Category 2 Clear button
        function cat2Clear(){
            document.getElementById("cat2InputTitle").value = '';
            document.getElementById("cat2InputJournalName").value = '';
            document.getElementById("cat2InputAcceptDate2").value = '';
            document.getElementById("cat2CheckPublication2").checked = false;
            document.getElementById("cat2CheckAccepted2").checked = false;
            document.getElementById("cat2InputPublication2").value = '';
            document.getElementById("cat2InputRelevantURL").value = '';
            document.getElementById('cat2UploadFile').value = '';
            // Clear error msg
            document.getElementById("cat2TitleErrorMsg").style.display = "none";
            document.getElementById("cat2JournalNameErrorMsg").style.display = "none";
            document.getElementById("cat2AcceptDateErrorMsg").style.display = "none";
            document.getElementById("cat2FileUploadErrorMsg").style.display = "none";
            document.getElementById("cat2PublicationInfoErrorMsg").style.display = "none";
            document.getElementById("cat2URLErrorMsg").style.display = "none";
            document.getElementById("cat2AtLeastOne").style.display = "none";
            // Items to be checked are not visible
            document.getElementById('cat2InputAcceptDate2').style.display = "none";
            document.getElementById('cat2AcceptDateLable2').style.display = "none";
            document.getElementById('cat2InputPublication2').style.display = "none";
            document.getElementById('cat2PublicationLable2').style.display = "none";
            document.getElementById('cat2UrlLable').style.display = "none";
            document.getElementById('cat2InputRelevantURL').style.display = "none";
            document.getElementById('cat2UploadFile').style.display = "none";
            document.getElementById('cat2fileUploadLable').style.display = "none";

            //  When it clicks the clear button, Enable the Add button
            var cat2AddBtn = document.getElementById('categoryTwoAddBtn');
            cat2AddBtn.disabled = false;

            var panelTitleID = 'cat2PanelTitle';
            resetAllPanel(panelTitleID);

        }
    </script>
<?php /**PATH D:\Development\research_allowness\resources\views/frontend/application/component/category02.blade.php ENDPATH**/ ?>