@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-lightbulb-o"></i> Category 7: Research Proposals (Progress Report)
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category7aForm" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Previous Proposal Title -->
        <div class="form-group row">
          <label for="titleOfResearchProposal" class="col-sm-12 col-form-label">You have submitted a research proposal. Please provide the title: <span class="text-danger">*</span></label>
          <div class="col-sm-12">
            <input type="text" class="form-control" name="titleOfResearchProposal" id="titleOfResearchProposal" placeholder="Title of the research proposal">
          </div>
        </div>

        <!-- Upload Progress Report -->
        <div class="form-group row">
          <label for="cat7aFileUpload" class="col-sm-12 col-form-label">Upload the progress report <span class="text-danger">*</span></label>
          <div class="col-sm-10">
            <input type="file" class="form-control" name="cat7File" id="cat7aFileUpload" accept=".pdf">
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 7 (Progress Report)
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category7aForm').reset();
}
</script>

@endsection
