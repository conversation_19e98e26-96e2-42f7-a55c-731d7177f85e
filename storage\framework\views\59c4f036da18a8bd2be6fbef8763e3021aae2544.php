
<?php $__env->startSection('admin'); ?>

<!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Dashboard</h1>

          </div><!-- /.col -->
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="#">Home</a></li>
              <li class="breadcrumb-item active">Dashboard</li>
            </ol>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <style>

    </style>
    <br>
    <section class="content">
      <div class="container-fluid">
        <!-- <div class="alert alert-danger" id="success-danger">
          <button type="button" class="close text-white" data-dismiss="alert">x</button>
          <strong>Notice!&nbsp;&nbsp;</strong>
          USJNet Services Tempory unavailable.in case department head functionality may have some dealy.
       </div> -->
        <div class="row">
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h3><?php echo e($applicants); ?></h3>

                <p>Applicants</p>
              </div>
              <div class="icon bg-white">
                <i class="ion ion-ios-people"></i>
              </div>
              <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h3><?php echo e($applications); ?></h3>

                <p>Application</p>
              </div>
              <div class="icon bg-white">
                <i class="ion ion-calendar"></i>
              </div>
              <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h3><?php echo e($incompleteApllication); ?></h3>

                <p>Draft Application</p>
              </div>
              <div class="icon bg-white">
                <i class="ion ion-university"></i>
              </div>
              <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-primary">
              <div class="inner">
                <h3><?php echo e($completeApplication); ?></h3>

                <p>Complete Application</p>
              </div>
              <div class="icon bg-white">
                <i class="ion ion-trophy"></i>
              </div>
              <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          <!-- ./col -->
        </div>
        <!-- /.row -->
        <div class="row">
            <div class="col-lg-3 col-6">
              <!-- small box -->
              <div class="small-box bg-primary">
                <div class="inner">
                  <h3><?php echo e($scApplication); ?></h3>

                  <p>Subject Clerk Pending</p>
                </div>
                <div class="icon bg-white">
                  <i class="ion ion-android-contact"></i>
                </div>
                <a href="<?php echo e(route('app.show')); ?>" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
              </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-3 col-6">
              <!-- small box -->
              <div class="small-box bg-primary">
                <div class="inner">
                  <h3><?php echo e($libApplication); ?></h3>

                  <p>Librarian Pending</p>
                </div>
                <div class="icon bg-white">
                  <i class="ion ion-arrow-return-left"></i>
                </div>
                <a href="<?php echo e(route('librarian.list.open')); ?>" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
              </div>
            </div>
            <div class="col-lg-3 col-6">
              <!-- small box -->
              <div class="small-box bg-primary">
                <div class="inner">
                  <h3><?php echo e($cochairApplication); ?></h3>

                  <p>Co-Chair Pending</p>
                </div>
                <div class="icon bg-white">
                  <i class="ion ion-log-out"></i>
                </div>
                <a href="<?php echo e(route('app.cochair.view')); ?>" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
              </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-3 col-6">
              <!-- small box -->
              <div class="small-box bg-primary">
                <div class="inner">
                  <h3><?php echo e($vcApplication); ?></h3>

                  <p>Vice Chancellor Pending</p>
                </div>
                <div class="icon bg-white">
                  <i class="ion ion-shuffle"></i>
                </div>
                <a href="<?php echo e(route('app.vc.view')); ?>" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
              </div>
            </div>

            <!-- ./col -->
          </div>
        <!-- /.row -->

        <!-- Main row -->

        <!-- /.row (main row) -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\research_allowness\resources\views/admin/index.blade.php ENDPATH**/ ?>