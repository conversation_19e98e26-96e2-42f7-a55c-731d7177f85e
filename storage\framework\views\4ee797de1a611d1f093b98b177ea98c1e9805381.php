<div class="panel panel-default">
        <a class="toggleLinks" data-toggle="collapse" data-parent="#accordion" href="#collapse5">
            <div class="panel-heading">
                <h5 class="panel-title" id="cat5PanelTitle">
                Category 5: Postgraduate Supervision
                </h5>
            </div>
        </a>
    <div id="collapse5" class="panel-collapse collapse">
        <div class="panel-body">
        <!-- *******************Category five form start****************** -->
        <form style="display: <?php echo $partFiveData !== null ? "none":"block" ?>;" id="cat5form">
            <!-- 5.1 -->
            <fieldset class="form-group">
                <div class="row">
                    <label for="lable" id="cat5UploadLable" class="col-sm-10 col-form-label">Upload a confirmation letter from Faculty of Graduate Studies/respective authority on supervising or intending to supervise postgraduate students engaged in clinical or research work in the current year <span class="asterisk">*</span></label>
                </div>
            </fieldset>
            <div class="form-group row" id="cat5FileUploadSection">
                <div class="col-sm-10">
                    <input type="file" name="cat5UploadFile" class="form-control" oninput="validatePDFCategory05()" id="cat5UploadFile" required accept=".pdf">
                    <p id="cat5FileUploadErrorMsg" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
                </div>
                <!-- Category five Add button -->
                <div class="col-md-1">
                    <button type="button" name="categoryFiveAdd" id="categoryFiveAddBtn" class="btn btn-primary add-btn" onclick="AddPartFiveRecoredToDb()">Submit</button>
                </div>
                <!-- Category Five Clear button -->
                <div class="col-md-1">
                    <button type="button" class="btn btn-warning clear-btn" id="categoryFiveClearBtn" onclick="cat5Clear()">Clear</button>
                </div>
            </div>

        </form>
        <!-- *******************form end******************** -->
        <!-- Table 5 start-->
        <div class="form-group row" style="overflow-x:auto; display:none;">
            <table id="categoryFiveTable" class="table table-bordered">
                <thead>
                    <tr>
                        <th scope="col">No</th>
                        <th scope="col">File Name</th>
                        <th scope="col">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                        <?php $partFiverowcount = 0; ?>
                                <?php if($partFiveData !== null): ?>
                                    <?php $__currentLoopData = $partFiveData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php $partFiverowcount = ++$partFiverowcount; ?>
                                        <tr>
                                          <td><?php echo e($partFiverowcount); ?></td>
                                          <td>
                                            <?php if($row->uploadFile): ?>
                                            <span class="badge badge-pill badge-success">Uploaded</span>
                                            <?php else: ?>
                                            <span class="badge badge-pill badge-dark">No Attachment</span>
                                            <?php endif; ?>
                                        </td>
                                          <td><button class="btn btn-sm btn-danger" onclick="deleteDBrowPart5('<?php echo e($row->id); ?>','<?php echo e($partFiverowcount); ?>','1')">Delete</button></td>
                                        </tr>

                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <?php endif; ?>
                        <!-- data will be add -->
                    </tbody>
            </table>
        </div>
		<!-- Table 5 end -->
        </div>
      </div>
    </div>


<!-- Catagory 5 lower table -->
<script>
    function cat5AddRow(insertedId) {

        // Validate category 5 and add new row
        // Get the file input element
        const fileName = document.getElementById('cat5UploadFile').value;
        if(fileName == ""){
            // Display error message
            document.getElementById('cat5FileUploadErrorMsg').style.display = 'block';
        }else{
            // Get the table reference
            const table = document.getElementById('categoryFiveTable').getElementsByTagName('tbody')[0];

            // Create a new row
            var newRow = table.insertRow();

            // Insert cells
            var cell1 = newRow.insertCell(0);
            var cell2 = newRow.insertCell(1);
            var cell3 = newRow.insertCell(2);

            // Populate cells with form values
            cell1.innerHTML = table.rows.length; // Auto-increment No column
            cell2.innerHTML = fileName ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';
            cell3.innerHTML = '<span class="btn btn-sm btn-danger" onclick="deleteDBrowPart5('+ insertedId +',1,0)">Delete</span>';

            // Clear form values and hide the form
            document.getElementById('cat5UploadFile').value = '';
            document.getElementById('cat5FileUploadSection').style.display = 'none';
            document.getElementById('cat5UploadLable').style.display = 'none';
            document.getElementById('categoryFiveAddBtn').style.display = 'none';
            document.getElementById('categoryFiveClearBtn').style.display = 'none';
        }

    }

    // Category 5 add to DB *******************************
    // Function to delete a row from the table
    function cat5DeleteRow(row,accNo) {
        if(accNo == '1'){
            categoryFiveTable.deleteRow(row.rowIndex);
        }else{
            categoryFiveTable.deleteRow(row.rowIndex);
        }

        // collapse - enable
        var panelTitleID = 'cat5PanelTitle';
        resetAllPanel(panelTitleID);

    }

    // Delete in the row of database
    function deleteDBrowPart5(deletedId,tblRowId,accNo){
        //   Catagory Five - Ajax request for delete row
        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
            $.ajax({

                url: '/deletePartFiveRecord',
                type: 'POST',
                data: {_token: CSRF_TOKEN,
                    deletedId: deletedId,
                },
                dataType: 'JSON',

                success: function (data) {
                    if(data.status){
                        cat5DeleteRow(tblRowId,accNo);
                    }else{
                        alert("Error");
                    }

                },
                error: function(xhr, status, error) {
                    alert("Somethig went worng");
                }

            });
    }

    function AddPartFiveRecoredToDb(){
        if(validateCategory05()){
            // Get values from form
            // Get the file input element
            const cat5File = document.getElementById('cat5UploadFile').value;
            const panelTitle = document.getElementById('cat5PanelTitle');
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');

            const cat5File1 = document.getElementById('cat5UploadFile');
            var files = $('#cat5UploadFile')[0].files;
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');

            var uploadedData = new FormData();
            uploadedData.append('_token',CSRF_TOKEN);
            uploadedData.append('file',files[0]);

            // Add button disable
            var cat5AddBtn = document.getElementById('categoryFiveAddBtn');
            // cat5AddBtn.disabled = true;

        //   Catagory Five - Ajax request

        Swal.fire({
            title: "Are you sure that you want to submit?",
            text: "You will not be able to edit the application after submitting it",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes. Submit"
            
        }).then((result) => {
            if (result.isConfirmed) {
                // Ajax start
                $.ajax({
                    url: '/createPartFiveRecord',
                    method: 'post',
                    data: uploadedData,
                    contentType: false,
                    processData: false,
                    dataType: 'json',
                    success: function(data) {
                        if(data.status){
                            cat5AddRow(data.insertedId);
                            // Reset the text color of the panel title
                            panelTitle.style.color = '#021691';

                            // Get elements by class name
                            var toggleLinks = document.getElementsByClassName('toggleLinks');

                            // Loop through the elements and update the data-toggle attribute
                            for (var i = 0; i < toggleLinks.length; i++) {
                                // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                                toggleLinks[i].setAttribute('data-toggle', 'noName');
                            }

                            // Enable the submit and save draft button
                            submitButton.disabled = false;
                            saveDraftButton.disabled = false;

                            //  If successful, Enable the Add button
                            cat5AddBtn.disabled = false;

                            // Redirect to the application.store route
                            window.location.href = "<?php echo e(route('application.store')); ?>";
                        }

                    },
                    error: function(error) {
                        alert("somethig went worng")
                    }
                });
                // Ajax end
                    Swal.fire({
                        title: "Success!",
                        // text: "Your application has been submitted successfuly.",
                        icon: "success"
                    });
            }
        });

            
        }

    }

    function cat5DeleteRow(tblRowId,accNo) {

        categoryFiveTable.deleteRow(1);

        // Show the form fields
        document.getElementById('cat5form').style.display = 'block';
        document.getElementById('cat5UploadFile').value = '';
        document.getElementById('cat5FileUploadSection').style.display = 'block';
        document.getElementById('cat5UploadLable').style.display = 'block';
        document.getElementById('categoryFiveAddBtn').style.display = 'block';
        document.getElementById('categoryFiveClearBtn').style.display = 'block';

        // collapse - enable
        var panelTitleID = 'cat5PanelTitle';
        resetAllPanel(panelTitleID);
    }

    // Category 5 Validation
    function validateCategory05(){
        // Get the file input element
        const fileName = document.getElementById('cat5UploadFile').value;
        if(fileName == ""){
            // Display error message
            document.getElementById('cat5FileUploadErrorMsg').style.display = 'block';
            return false;
        }else{
            document.getElementById('cat5FileUploadErrorMsg').style.display = 'none';
        }
        return true;
    }
    //Validata upload only .pdf for Category Five
    function validatePDFCategory05() {
            // Get the file input element
            var fileInput = document.getElementById('cat5UploadFile');
            const panelTitle = document.getElementById('cat5PanelTitle');
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');
            // Get elements by class name
            var toggleLinks = document.getElementsByClassName('toggleLinks');


            // Get the selected file
            var selectedFile = fileInput.files[0];

            // Maximum file size in bytes (35MB)
            var maxSize = 35 * 1024 * 1024;

            // Check if a file is selected
            if (selectedFile) {
                // Get the file extension
                var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

                // Check if the file extension is not 'pdf'
                if (fileExtension !== 'pdf') {
                    // Display error message for incorrect file type
                    document.getElementById('cat5FileUploadErrorMsg').innerText = '*Please upload a .pdf file.*';
                    document.getElementById('cat5FileUploadErrorMsg').style.display = 'block';

                    // isValidateCat5 = true;

                    // Reset the text color of the panel title
                    panelTitle.style.color = '#021691';

                    // Loop through the elements and update the data-toggle attribute
                    for (var i = 0; i < toggleLinks.length; i++) {
                        // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                        toggleLinks[i].setAttribute('data-toggle', 'noName');
                    }

                    // Enable the submit button
                    submitButton.disabled = false;
                    saveDraftButton.disabled = false;

                    // Clear the file input
                    fileInput.value = '';
                } 
                // Check if file size exceeds 35MB
                else if (selectedFile.size > maxSize) {
                    // Display error message for file size exceeding 35MB
                    document.getElementById('cat5FileUploadErrorMsg').innerText = '*File size should not exceed 35MB.*';
                    document.getElementById('cat5FileUploadErrorMsg').style.display = 'block';

                    // Clear the file input
                    fileInput.value = '';
                } else {
                    // Hide error message
                    document.getElementById('cat5FileUploadErrorMsg').style.display = 'none';
                    // Change the text color of the panel title
                    panelTitle.style.color = '#f2820a';

                    // Loop through the elements and update the data-toggle attribute
                    for (var i = 0; i < toggleLinks.length; i++) {
                        toggleLinks[i].setAttribute('data-toggle', 'noName');
                    }

                    // Disable the submit and save draft button
                    submitButton.disabled = true;
                    saveDraftButton.disabled = true;

                    // isValidateCat5 = true;
                }
            }
        }

     // Category 5 Clear button
     function cat5Clear(){
            document.getElementById('cat5UploadFile').value = '';
            // Hide error message
            document.getElementById('cat5FileUploadErrorMsg').style.display = 'none';
            
            //  When it clicks the clear button, Enable the Add button
            var cat5AddBtn = document.getElementById('categoryFiveAddBtn');
            cat5AddBtn.disabled = false;

            var panelTitleID = 'cat5PanelTitle';
            resetAllPanel(panelTitleID);

        }
</script>
<?php /**PATH D:\Development\research_allowness\resources\views/frontend/application/component/category05.blade.php ENDPATH**/ ?>