<?php

namespace App\Http\Controllers\Application;

use App\Http\Controllers\Controller;
use App\Models\Application;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    public function __construct()
    {
        $currentURL = url()->current();
        session()->put('special_callback_url', $currentURL);
        session()->put('special_value', false);
        session()->put('checkReturn',  true);
        $this->middleware('auth');
        $this->middleware('role:applicant');
    }

    public function index()
    {
        session()->put('special_value', true);
        session()->forget('special_callback_url');
        $empNo = Auth()->user()->empNo;
        $name = Auth()->user()->name;
        $year = now()->format('Y');
        $ApplyvisibleStatus = 0;


        //check for unprocessing appplication
        $unprocessedApplicationCount = Application::where('empID', $empNo)->where('year', $year)->where('submittedStatus', 1)->where('applicationFinalDecision', 0)->count();

        //check for approved appplication
        $completedApplicationCount = Application::where('empID', $empNo)->where('year', $year)->where('submittedStatus', 1)->where('applicationFinalDecision', 1)->count();

        if ($unprocessedApplicationCount == 0 && $completedApplicationCount == 0) {
            $ApplyvisibleStatus = 1;
        }

        return view('frontend.application_new.index', compact('ApplyvisibleStatus', 'unprocessedApplicationCount', 'completedApplicationCount'));
    }

    public function ApplicationCategoryList(){

        return view('frontend.application_new.category_list');
    }

    public function ApplicationIndexJournalPublication(){

        return view('frontend.application_new.component.category1');
    }

    public function ApplicationIndexJournalPublicationConsecutive(){

        return view('frontend.application_new.component.category1a');
    }

    public function ApplicationOtherPublication(){

        return view('frontend.application_new.component.category2');
    }

    public function ApplicationResearchGrant(){

        return view('frontend.application_new.component.category3');
    }

    public function ApplicationOtherScholarlyWork(){

        return view('frontend.application_new.component.category4');
    }

    public function ApplicationPostgraduateSupervision(){

        return view('frontend.application_new.component.category5');
    }
    public function ApplicationPostgraduateStudies(){

        return view('frontend.application_new.component.category6');
    }
    public function ApplicationResearchProposal(){

        return view('frontend.application_new.component.category7');
    }
    public function ApplicationResearchProposalProgress(){

        return view('frontend.application_new.component.category7a');
    }
    public function ApplicationResearchProposalFinal(){

        return view('frontend.application_new.component.category7b');
    }
    public function ApplicationInnovationInvention(){

        return view('frontend.application_new.component.category8');
    }
}
