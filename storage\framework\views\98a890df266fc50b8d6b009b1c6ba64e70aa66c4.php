<div class="panel panel-default">
    <a class="toggleLinks" data-toggle="collapse" data-parent="#accordion" href="#collapse8">
        <div class="panel-heading">
            <h5 class="panel-title" id="cat8PanelTitle">
            Category 8: Innovation and Invention
            </h5>
        </div>
    </a>
<div id="collapse8" class="panel-collapse collapse">
    <div class="panel-body">
    <!-- *******************Category eight form start****************** -->
    <form style="display: <?php echo $partEightData !== null ? "none" : "block"; ?>" id="cat8Form">
        <!-- 8-a -->
        <fieldset class="form-group">
            <div class="row">
                <label for="lable" id="cat8UploadLable1" class="col-sm-10 col-form-label">Upload the Invention Disclosure Form approved by the University Business Linkage Cell <span class="asterisk">*</span></label>
            </div>
        </fieldset>
        <div class="form-group row" id="cat8FileUploadSection1">
            <div class="col-sm-10">
                <input type="file" name="cat8UploadFile1" class="form-control" oninput="validatePDFCategory08a();validateCategory08();" id="cat8InventionFile" accept=".pdf">
                <p id="cat8FileUploadErrorMsg1" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
            </div>
        </div>
        <!-- 8-b -->
        <fieldset class="form-group">
            <div class="row">
                <label for="lable" id="cat8UploadLable2" class="col-sm-10 col-form-label">Upload the Evidence to show the product in the market or service being used in commercially</label>
            </div>
        </fieldset>
        <div class="form-group row" id="cat8FileUploadSection2">
            <div class="col-sm-10">
                <input type="file" name="cat8UploadFile2" class="form-control" oninput="validatePDFCategory08b();validateCategory08();" id="cat8EvidenceFile" accept=".pdf">
                <p id="cat8FileUploadErrorMsg2" style="display:none" class="allInputErrorMsg"> *Please upload a .pdf file* </p>
            </div>
            <!-- Category Eight draft button -->
            <div class="col-sm-3 col-md-1">
                <button type="button" name="categoryEightAdd" id="categoryEightAddBtn" class="btn btn-primary add-btn" onclick="AddPartEightRecoredToDb()">Submit</button>
            </div>
            <!-- Category Eight Clear button -->
            <div class="col-sm-3 col-md-1">
                <button type="button" class="btn btn-warning clear-btn" onclick="cat8Clear()">Clear</button>
            </div>
        </div>

    </form>
    <!-- *******************form end******************** -->
    <!-- Table 8 start-->
    <div class="form-group row" style="overflow-x:auto; display:none;">
        <table id="categoryEightTable" class="table table-bordered">
            <thead>
                <tr>
                    <th scope="col">No</th>
                    <th scope="col">Invention File</th>
                    <th scope="col">Evidence File</th>
                    <th scope="col">Action</th>
                </tr>
                </thead>
                <tbody>
                    <?php $partEightrowcount = 0; ?>
                        <?php if($partEightData !== null): ?>
                            <?php $__currentLoopData = $partEightData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $partEightrowcount = ++$partEightrowcount; ?>
                                <tr id="parteight<?php echo e($index+1); ?>">
                                    <td><?php echo e($index+1); ?></td>
                                    <td>
                                        <?php if($row->inventionFile): ?>
                                        <span class="badge badge-pill badge-success">Uploaded</span>
                                        <?php else: ?>
                                        <span class="badge badge-pill badge-dark">No Attachment</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($row->evidenceFile): ?>
                                        <span class="badge badge-pill badge-success">Uploaded</span>
                                        <?php else: ?>
                                        <span class="badge badge-pill badge-dark">No Attachment</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><button class="btn btn-sm btn-danger" onclick="deleteDBrowPart8('<?php echo e($row->id); ?>','<?php echo e($index+1); ?>','1')">Delete</button></td>
                                </tr>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <?php endif; ?>
                    <!-- data will be add -->
                </tbody>
        </table>
    </div>
    <!-- Table 8 end -->
    </div>
  </div>
</div>

<!-- Catagory 8 lower table -->
<script>
    function cat8AddRow(insertedId) {

        // Validate category 8 and add new row
        // Get the file input element
        const inventionFileName = document.getElementById('cat8InventionFile').value;
        const evidenceFileName = document.getElementById('cat8EvidenceFile').value;
        if(inventionFileName == ""){
            // Display error message
            document.getElementById('cat8FileUploadErrorMsg1').style.display = 'block';

        // }else if(evidenceFileName == ""){
        //     document.getElementById('cat8FileUploadErrorMsg2').style.display = 'block';

        }else{
            // Get the table reference
            const table = document.getElementById('categoryEightTable').getElementsByTagName('tbody')[0];

            // Create a new row
            var newRow = table.insertRow();

             // Insert cells
            var cell1 = newRow.insertCell(0);
            var cell2 = newRow.insertCell(1);
            var cell3 = newRow.insertCell(2);
            var cell4 = newRow.insertCell(3);

            // Populate cells with form values
            cell1.innerHTML = table.rows.length; // Auto-increment No column
            cell2.innerHTML = inventionFileName ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';
            cell3.innerHTML = evidenceFileName ? '<span class="badge badge-pill badge-success">Uploaded</span>' : '<span class="badge badge-pill badge-dark">No Attachment</span>';
            cell4.innerHTML = '<span class="btn btn-sm btn-danger" onclick="deleteDBrowPart8('+ insertedId +',1,0)">Delete</span>';


            // Set the text of the cell elements
            // noCell.textContent = table.rows.length;
            // inventionFileNameCell.textContent = inventionFileName;
            // evidenceFileNameCell.textContent = evidenceFileName;
            // actionCell.innerHTML = '<span class="btn btn-sm btn-danger" onclick="deleteDBrowPart8('+ insertedId +',1,0)">Delete</span>';

            // Clear form values and hide the form
            document.getElementById('cat8InventionFile').value = '';
            document.getElementById('cat8EvidenceFile').value = '';
            document.getElementById('cat8Form').style.display = 'none';
        }

    }


    // Delete in the row of database
    function deleteDBrowPart8(deletedId,tblRowId,accNo){
        //   Catagory Eight - Ajax request for delete row

        var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');
            $.ajax({

                url: '/deletePartEightRecord',
                type: 'POST',
                data: {_token: CSRF_TOKEN,
                    deletedId: deletedId,
                },
                dataType: 'JSON',

                success: function (data) {
                    if(data.status){
                        cat8DeleteRow(tblRowId,accNo);
                    }else{
                        alert("Error");
                    }

                },
                error: function(xhr, status, error) {
                    alert("Somethig went worng");
                }

            });
    }
    function AddPartEightRecoredToDb(){
        if(validateCategory08()){
            // Get values from form
            // Get the file input element
            const inventionFileName = document.getElementById('cat8InventionFile').value;
            const evidenceFileName = document.getElementById('cat8EvidenceFile').value;
            const panelTitle = document.getElementById('cat8PanelTitle');
            const submitButton = document.getElementById('finalSubmitBtn');
            const saveDraftButton = document.getElementById('finalSaveDraftBtn');

            // const cat5File1 = document.getElementById('cat5UploadFile');
            var file1 = $('#cat8InventionFile')[0].files;
            var file2 = $('#cat8EvidenceFile')[0].files;
            var CSRF_TOKEN = $('meta[name="csrf-token"]').attr('content');

            var uploadedData = new FormData();
            uploadedData.append('_token',CSRF_TOKEN);
            uploadedData.append('file1',file1[0]);
            uploadedData.append('file2',file2[0]);

            // Add button disable
            var cat8AddBtn = document.getElementById('categoryEightAddBtn');
            // cat8AddBtn.disabled = true;

        //   Catagory Eight - Ajax request

        Swal.fire({
            title: "Are you sure that you want to submit?",
            text: "You will not be able to edit the application after submitting it",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes. Submit"
            
        }).then((result) => {
            if (result.isConfirmed) {
                // Ajax start
                    $.ajax({
                    url: '/createPartEightRecord',
                    method: 'post',
                    data: uploadedData,
                    contentType: false,
                    processData: false,
                    dataType: 'json',
                    success: function(data) {
                        if(data.status){
                            cat8AddRow(data.insertedId);
                            // Reset the text color of the panel title
                            panelTitle.style.color = '#021691';

                            // Get elements by class name
                            var toggleLinks = document.getElementsByClassName('toggleLinks');

                            // Loop through the elements and update the data-toggle attribute
                            for (var i = 0; i < toggleLinks.length; i++) {
                                // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                                toggleLinks[i].setAttribute('data-toggle', 'noName');
                            }

                            // Enable the submit and save draft button
                            submitButton.disabled = false;
                            saveDraftButton.disabled = false;

                            //  If successful, Enable the Add button
                            cat8AddBtn.disabled = false;

                            
                            // Redirect to the application.store route
                            window.location.href = "<?php echo e(route('application.store')); ?>";
                        }

                        },
                        error: function(error) {
                            alert("somethig went worng")
                        }
                    });
                // Ajax end
                    Swal.fire({
                        title: "Success!",
                        // text: "Your application has been submitted successfuly.",
                        icon: "success"
                    });
            }
        });

            
        }

    }

    function cat8DeleteRow(tblRowId,accNo) {

        categoryEightTable.deleteRow(1);
        // Show the form fields
        document.getElementById('cat8Form').style.display = 'block';

        // collapse - enable
        var panelTitleID = 'cat8PanelTitle';
        resetAllPanel(panelTitleID);
    }

    // Category 8 Validation
    function validateCategory08(){
        // Get the file input element
        const inventionFileName = document.getElementById('cat8InventionFile').value;
        const evidenceFileName = document.getElementById('cat8EvidenceFile').value;
        // Get values for panel disable
        const panelTitle = document.getElementById('cat8PanelTitle');
        const submitButton = document.getElementById('finalSubmitBtn');
        const saveDraftButton = document.getElementById('finalSaveDraftBtn');
        // Get elements by class name
        var toggleLinks = document.getElementsByClassName('toggleLinks');

        var isValidateCat8 = true;
        if(inventionFileName == ""){
            // Display error message
            if(inventionFileName == ""){
                document.getElementById('cat8FileUploadErrorMsg1').style.display = 'block';
            }
            // if(evidenceFileName == ""){
            //     document.getElementById('cat8FileUploadErrorMsg2').style.display = 'block';
            // }


            isValidateCat8 = false;
        }else{
            document.getElementById('cat8FileUploadErrorMsg1').style.display = 'none';
            document.getElementById('cat8FileUploadErrorMsg2').style.display = 'none';
        }

        // check for the value is added or not
        if(inventionFileName != ''){
                // Change the text color of the panel title
                panelTitle.style.color = '#f2820a';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Disable the submit and save draft button
                submitButton.disabled = true;
                saveDraftButton.disabled = true;
            }else{
                // Reset the text color of the panel title
                panelTitle.style.color = '#021691';

                // Loop through the elements and update the data-toggle attribute
                for (var i = 0; i < toggleLinks.length; i++) {
                    // toggleLinks[i].setAttribute('data-toggle', 'collapse');
                    toggleLinks[i].setAttribute('data-toggle', 'noName');
                }

                // Enable the submit button
                submitButton.disabled = false;
                saveDraftButton.disabled = false;
            }

        return isValidateCat8;
    }

    //Validata upload only .pdf for Category Eight - A - Invention File
    function validatePDFCategory08a() {
        // Get the file input element
        var fileInput = document.getElementById('cat8InventionFile');

        // Get the selected file
        var selectedFile = fileInput.files[0];

        // Maximum file size in bytes (35MB)
        var maxSize = 35 * 1024 * 1024;

        // Check if a file is selected
        if (selectedFile) {
            // Get the file extension
            var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

            // Check if the file extension is not 'pdf'
            if (fileExtension !== 'pdf') {
                // Display error message for incorrect file type
                document.getElementById('cat8FileUploadErrorMsg1').innerText = '*Please upload a .pdf file.*';
                document.getElementById('cat8FileUploadErrorMsg1').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            } 
            // Check if file size exceeds 35MB
            else if (selectedFile.size > maxSize) {
                // Display error message for file size exceeding 35MB
                document.getElementById('cat8FileUploadErrorMsg1').innerText = '*File size should not exceed 35MB.*';
                document.getElementById('cat8FileUploadErrorMsg1').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            } 
            else {
                // Hide error message
                document.getElementById('cat8FileUploadErrorMsg1').style.display = 'none';
            }
        }
    }

    //Validata upload only .pdf for Category Eight - B - Evidence File
    function validatePDFCategory08b() {
        // Get the file input element
        var fileInput = document.getElementById('cat8EvidenceFile');

        // Get the selected file
        var selectedFile = fileInput.files[0];

        // Maximum file size in bytes (35MB)
        var maxSize = 35 * 1024 * 1024;

        // Check if a file is selected
        if (selectedFile) {
            // Get the file extension
            var fileExtension = selectedFile.name.split('.').pop().toLowerCase();

            // Check if the file extension is not 'pdf'
            if (fileExtension !== 'pdf') {
                // Display error message for incorrect file type
                document.getElementById('cat8FileUploadErrorMsg2').innerText = '*Please upload a .pdf file.*';
                document.getElementById('cat8FileUploadErrorMsg2').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            } 
            // Check if file size exceeds 35MB
            else if (selectedFile.size > maxSize) {
                // Display error message for file size exceeding 35MB
                document.getElementById('cat8FileUploadErrorMsg2').innerText = '*File size should not exceed 35MB.*';
                document.getElementById('cat8FileUploadErrorMsg2').style.display = 'block';

                // Clear the file input
                fileInput.value = '';
            } 
            else {
                // Hide error message
                document.getElementById('cat8FileUploadErrorMsg2').style.display = 'none';
            }
        }
    }

    // Category 8 Clear button
    function cat8Clear(){
            document.getElementById('cat8InventionFile').value = '';
            document.getElementById('cat8EvidenceFile').value = '';
            // Clear error msg
            document.getElementById("cat8FileUploadErrorMsg1").style.display = "none";
            document.getElementById("cat8FileUploadErrorMsg2").style.display = "none";

            //  When it clicks the clear button, Enable the Add button
            var cat8AddBtn = document.getElementById('categoryEightAddBtn');
            cat8AddBtn.disabled = false;

            var panelTitleID = 'cat8PanelTitle';
            resetAllPanel(panelTitleID);

        }
</script>
<?php /**PATH D:\Development\research_allowness\resources\views/frontend/application/component/category08.blade.php ENDPATH**/ ?>