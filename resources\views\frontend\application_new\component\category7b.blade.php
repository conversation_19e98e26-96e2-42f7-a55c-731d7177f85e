@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-lightbulb-o"></i> Category 7: Research Proposals (Final Report)
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category7bForm" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Instructions -->
        <div class="alert alert-success">
          <h5><i class="icon fa fa-check-circle"></i> Final Report</h5>
          Please provide the final report for your completed research proposal.
        </div>

        <!-- Original Proposal Reference -->
        <div class="form-group row">
          <label for="originalProposalRef" class="col-sm-3 col-form-label">
            Original Proposal Reference <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="originalProposalRef" name="original_proposal_ref"
                   placeholder="Enter the reference number of your original proposal" required>
          </div>
        </div>

        <!-- Proposal Title -->
        <div class="form-group row">
          <label for="proposalTitle" class="col-sm-3 col-form-label">
            Proposal Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="proposalTitle" name="proposal_title" rows="2"
                      placeholder="Enter the title of your research proposal" required></textarea>
          </div>
        </div>

        <!-- Completion Date -->
        <div class="form-group row">
          <label for="completionDate" class="col-sm-3 col-form-label">
            Completion Date <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="date" class="form-control" id="completionDate" name="completion_date" required>
          </div>
        </div>

        <!-- Objectives Achieved -->
        <div class="form-group row">
          <label for="objectivesAchieved" class="col-sm-3 col-form-label">
            Objectives Achieved <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="objectivesAchieved" name="objectives_achieved" rows="4"
                      placeholder="Describe all objectives that were achieved" required></textarea>
          </div>
        </div>

        <!-- Research Findings -->
        <div class="form-group row">
          <label for="researchFindings" class="col-sm-3 col-form-label">
            Key Research Findings <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="researchFindings" name="research_findings" rows="5"
                      placeholder="Describe the key findings and results of your research" required></textarea>
          </div>
        </div>

        <!-- Publications Resulted -->
        <div class="form-group row">
          <label for="publicationsResulted" class="col-sm-3 col-form-label">
            Publications Resulted <small>(Optional)</small>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="publicationsResulted" name="publications_resulted" rows="3"
                      placeholder="List any publications that resulted from this research"></textarea>
          </div>
        </div>

        <!-- Impact and Significance -->
        <div class="form-group row">
          <label for="impactSignificance" class="col-sm-3 col-form-label">
            Impact and Significance <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="impactSignificance" name="impact_significance" rows="4"
                      placeholder="Describe the impact and significance of your research" required></textarea>
          </div>
        </div>

        <!-- Future Research Directions -->
        <div class="form-group row">
          <label for="futureDirections" class="col-sm-3 col-form-label">
            Future Research Directions <small>(Optional)</small>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="futureDirections" name="future_directions" rows="3"
                      placeholder="Describe potential future research directions"></textarea>
          </div>
        </div>

        <!-- Final Report Document Upload -->
        <div class="form-group row">
          <label for="finalReportDocument" class="col-sm-3 col-form-label">
            Final Report Document <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="finalReportDocument" name="final_report_document"
                   accept=".pdf,.doc,.docx" required>
            <small class="form-text text-muted">Upload comprehensive final report (PDF, DOC, DOCX) - Max: 50MB</small>
          </div>
        </div>

        <!-- Supporting Documents -->
        <div class="form-group row">
          <label for="supportingDocuments" class="col-sm-3 col-form-label">
            Supporting Documents <small>(Optional)</small>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="supportingDocuments" name="supporting_documents[]"
                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" multiple>
            <small class="form-text text-muted">Upload additional supporting documents (Multiple files allowed) - Max: 10MB each</small>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="button" class="btn btn-info mr-2" onclick="saveDraft()">
              <i class="fa fa-save"></i> Save as Draft
            </button>
            <button type="submit" class="btn btn-success">
              <i class="fa fa-check"></i> Submit Final Report
            </button>
          </div>
        </div>
      </form>

      <!-- Final Reports List -->
      <div class="mt-4">
        <h5>Submitted Final Reports</h5>
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead class="thead-dark">
              <tr>
                <th>No.</th>
                <th>Proposal Title</th>
                <th>Completion Date</th>
                <th>Submission Date</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="finalReportsTableBody">
              <tr>
                <td colspan="6" class="text-center text-muted">No final reports submitted yet</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category7bForm').reset();
}

function saveDraft() {
    alert('Draft saved successfully!');
}
</script>

@endsection
