<?php $__env->startSection('admin'); ?>

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-graduation-cap"></i> Category 4: Other Scholarly Work
      </h3>
      <div class="card-tools">
        <a href="<?php echo e(route('application.category.list')); ?>" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category4Form" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <!-- Instructions -->
        <div class="alert alert-info">
          <h5><i class="icon fa fa-info"></i> Instructions</h5>
          Please provide details of other scholarly work including editorial work, peer reviews, academic awards, etc.
        </div>

        <!-- Scholarly Work Type -->
        <div class="form-group row">
          <label for="scholarlyWorkType" class="col-sm-3 col-form-label">
            Type of Scholarly Work <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="scholarlyWorkType" name="scholarly_work_type" required>
              <option value="">Select Type</option>
              <option value="Editorial Work">Editorial Work</option>
              <option value="Peer Review">Peer Review</option>
              <option value="Academic Award">Academic Award</option>
              <option value="Professional Recognition">Professional Recognition</option>
              <option value="Keynote/Invited Speaker">Keynote/Invited Speaker</option>
              <option value="External Examiner">External Examiner</option>
              <option value="Committee Member">Committee Member</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>

        <!-- Title/Description -->
        <div class="form-group row">
          <label for="workTitle" class="col-sm-3 col-form-label">
            Title/Description <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="workTitle" name="work_title"
                   placeholder="Enter the title or description of your scholarly work" required>
          </div>
        </div>

        <!-- Organization/Institution -->
        <div class="form-group row">
          <label for="organization" class="col-sm-3 col-form-label">
            Organization/Institution <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="organization" name="organization"
                   placeholder="Enter the organization or institution name" required>
          </div>
        </div>

        <!-- Date/Period -->
        <div class="form-group row">
          <label for="workPeriod" class="col-sm-3 col-form-label">
            Date/Period <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <div class="row">
              <div class="col-sm-6">
                <input type="date" class="form-control" id="startDate" name="start_date" required>
                <small class="form-text text-muted">Start Date</small>
              </div>
              <div class="col-sm-6">
                <input type="date" class="form-control" id="endDate" name="end_date">
                <small class="form-text text-muted">End Date (if applicable)</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Your Role -->
        <div class="form-group row">
          <label for="yourRole" class="col-sm-3 col-form-label">
            Your Role <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="yourRole" name="your_role"
                   placeholder="Describe your role in this scholarly work" required>
          </div>
        </div>

        <!-- Evidence Document Upload -->
        <div class="form-group row">
          <label for="evidenceDocument" class="col-sm-3 col-form-label">
            Evidence Document <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="evidenceDocument" name="evidence_document"
                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
            <small class="form-text text-muted">Upload supporting evidence (PDF, DOC, DOCX, JPG, PNG) - Max: 10MB</small>
          </div>
        </div>

        <!-- Additional Details -->
        <div class="form-group row">
          <label for="additionalDetails" class="col-sm-3 col-form-label">
            Additional Details <small>(Optional)</small>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="additionalDetails" name="additional_details" rows="3"
                      placeholder="Provide any additional relevant details"></textarea>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="button" class="btn btn-info mr-2" onclick="saveDraft()">
              <i class="fa fa-save"></i> Save as Draft
            </button>
            <button type="submit" class="btn btn-success">
              <i class="fa fa-plus"></i> Add Scholarly Work
            </button>
          </div>
        </div>
      </form>

      <!-- Scholarly Work List -->
      <div class="mt-4">
        <h5>Added Scholarly Work</h5>
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead class="thead-dark">
              <tr>
                <th>No.</th>
                <th>Type</th>
                <th>Title</th>
                <th>Organization</th>
                <th>Period</th>
                <th>Role</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="scholarlyWorkTableBody">
              <tr>
                <td colspan="7" class="text-center text-muted">No scholarly work added yet</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category4Form').reset();
}

function saveDraft() {
    alert('Draft saved successfully!');
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\research_allowness\resources\views/frontend/application_new/component/category4.blade.php ENDPATH**/ ?>