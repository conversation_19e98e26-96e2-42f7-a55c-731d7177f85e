@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-users"></i> Category 5: Postgraduate Supervision
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category5Form" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Instructions -->
        <div class="alert alert-info">
          <h5><i class="icon fa fa-info"></i> Instructions</h5>
          Please provide details of your postgraduate student supervision activities.
        </div>

        <!-- Student Name -->
        <div class="form-group row">
          <label for="studentName" class="col-sm-3 col-form-label">
            Student Name <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="studentName" name="student_name"
                   placeholder="Enter the student's full name" required>
          </div>
        </div>

        <!-- Student Registration Number -->
        <div class="form-group row">
          <label for="registrationNumber" class="col-sm-3 col-form-label">
            Registration Number <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="registrationNumber" name="registration_number"
                   placeholder="Enter the student's registration number" required>
          </div>
        </div>

        <!-- Degree Type -->
        <div class="form-group row">
          <label for="degreeType" class="col-sm-3 col-form-label">
            Degree Type <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="degreeType" name="degree_type" required>
              <option value="">Select Degree Type</option>
              <option value="PhD">PhD</option>
              <option value="MPhil">MPhil</option>
              <option value="MSc">MSc (by Research)</option>
              <option value="MA">MA (by Research)</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>

        <!-- Research Title -->
        <div class="form-group row">
          <label for="researchTitle" class="col-sm-3 col-form-label">
            Research Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="researchTitle" name="research_title" rows="2"
                      placeholder="Enter the title of the student's research" required></textarea>
          </div>
        </div>

        <!-- Supervision Type -->
        <div class="form-group row">
          <label for="supervisionType" class="col-sm-3 col-form-label">
            Supervision Type <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="supervisionType" name="supervision_type" required>
              <option value="">Select Supervision Type</option>
              <option value="Main Supervisor">Main Supervisor</option>
              <option value="Co-Supervisor">Co-Supervisor</option>
              <option value="External Supervisor">External Supervisor</option>
            </select>
          </div>
        </div>

        <!-- Current Status -->
        <div class="form-group row">
          <label for="currentStatus" class="col-sm-3 col-form-label">
            Current Status <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="currentStatus" name="current_status" required>
              <option value="">Select Current Status</option>
              <option value="Ongoing">Ongoing</option>
              <option value="Completed">Completed</option>
              <option value="Thesis Submitted">Thesis Submitted</option>
              <option value="Viva Completed">Viva Completed</option>
              <option value="Graduated">Graduated</option>
            </select>
          </div>
        </div>

        <!-- Registration Date -->
        <div class="form-group row">
          <label for="registrationDate" class="col-sm-3 col-form-label">
            Registration Date <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="date" class="form-control" id="registrationDate" name="registration_date" required>
          </div>
        </div>

        <!-- Completion Date (if applicable) -->
        <div class="form-group row">
          <label for="completionDate" class="col-sm-3 col-form-label">
            Completion Date <small>(If completed)</small>
          </label>
          <div class="col-sm-9">
            <input type="date" class="form-control" id="completionDate" name="completion_date">
          </div>
        </div>

        <!-- Evidence Document Upload -->
        <div class="form-group row">
          <label for="evidenceDocument" class="col-sm-3 col-form-label">
            Evidence Document <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="evidenceDocument" name="evidence_document"
                   accept=".pdf,.doc,.docx" required>
            <small class="form-text text-muted">Upload supervision letter or certificate (PDF, DOC, DOCX) - Max: 10MB</small>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="button" class="btn btn-info mr-2" onclick="saveDraft()">
              <i class="fa fa-save"></i> Save as Draft
            </button>
            <button type="submit" class="btn btn-success">
              <i class="fa fa-plus"></i> Add Supervision
            </button>
          </div>
        </div>
      </form>

      <!-- Supervision List -->
      <div class="mt-4">
        <h5>Added Supervision Records</h5>
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead class="thead-dark">
              <tr>
                <th>No.</th>
                <th>Student Name</th>
                <th>Reg. No.</th>
                <th>Degree</th>
                <th>Supervision Type</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="supervisionTableBody">
              <tr>
                <td colspan="7" class="text-center text-muted">No supervision records added yet</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category5Form').reset();
}

function saveDraft() {
    alert('Draft saved successfully!');
}
</script>

@endsection
