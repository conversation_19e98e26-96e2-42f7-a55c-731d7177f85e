@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-university"></i> Category 6: Postgraduate Studies
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category6Form" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Instructions -->
        <div class="alert alert-info">
          <h5><i class="icon fa fa-info"></i> Instructions</h5>
          Please provide details of your own postgraduate studies and qualifications.
        </div>

        <!-- Degree Type -->
        <div class="form-group row">
          <label for="degreeType" class="col-sm-3 col-form-label">
            Degree Type <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="degreeType" name="degree_type" required>
              <option value="">Select Degree Type</option>
              <option value="PhD">PhD</option>
              <option value="MPhil">MPhil</option>
              <option value="MSc">MSc</option>
              <option value="MA">MA</option>
              <option value="MEd">MEd</option>
              <option value="MBA">MBA</option>
              <option value="LLM">LLM</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>

        <!-- Degree Title -->
        <div class="form-group row">
          <label for="degreeTitle" class="col-sm-3 col-form-label">
            Degree Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="degreeTitle" name="degree_title"
                   placeholder="Enter the full title of your degree" required>
          </div>
        </div>

        <!-- University/Institution -->
        <div class="form-group row">
          <label for="university" class="col-sm-3 col-form-label">
            University/Institution <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="university" name="university"
                   placeholder="Enter the name of the university or institution" required>
          </div>
        </div>

        <!-- Country -->
        <div class="form-group row">
          <label for="country" class="col-sm-3 col-form-label">
            Country <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="country" name="country"
                   placeholder="Enter the country" required>
          </div>
        </div>

        <!-- Study Period -->
        <div class="form-group row">
          <label for="studyPeriod" class="col-sm-3 col-form-label">
            Study Period <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <div class="row">
              <div class="col-sm-6">
                <input type="date" class="form-control" id="startDate" name="start_date" required>
                <small class="form-text text-muted">Start Date</small>
              </div>
              <div class="col-sm-6">
                <input type="date" class="form-control" id="endDate" name="end_date">
                <small class="form-text text-muted">End Date (if completed)</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Current Status -->
        <div class="form-group row">
          <label for="currentStatus" class="col-sm-3 col-form-label">
            Current Status <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="currentStatus" name="current_status" required>
              <option value="">Select Current Status</option>
              <option value="Ongoing">Ongoing</option>
              <option value="Completed">Completed</option>
              <option value="Thesis Submitted">Thesis Submitted</option>
              <option value="Viva Completed">Viva Completed</option>
              <option value="Graduated">Graduated</option>
              <option value="On Hold">On Hold</option>
            </select>
          </div>
        </div>

        <!-- Research Area -->
        <div class="form-group row">
          <label for="researchArea" class="col-sm-3 col-form-label">
            Research Area <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="researchArea" name="research_area"
                   placeholder="Enter your research area or field of study" required>
          </div>
        </div>

        <!-- Thesis Title -->
        <div class="form-group row">
          <label for="thesisTitle" class="col-sm-3 col-form-label">
            Thesis/Dissertation Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <textarea class="form-control" id="thesisTitle" name="thesis_title" rows="2"
                      placeholder="Enter the title of your thesis or dissertation" required></textarea>
          </div>
        </div>

        <!-- Evidence Document Upload -->
        <div class="form-group row">
          <label for="evidenceDocument" class="col-sm-3 col-form-label">
            Evidence Document <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="evidenceDocument" name="evidence_document"
                   accept=".pdf,.doc,.docx" required>
            <small class="form-text text-muted">Upload enrollment letter, transcript, or degree certificate (PDF, DOC, DOCX) - Max: 10MB</small>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 6
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category6Form').reset();
}
</script>

@endsection
