@extends('frontend.frontend_master')
@section('admin')

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-users"></i> Category 5: Postgraduate Supervision
      </h3>
      <div class="card-tools">
        <a href="{{ route('application.category.list') }}" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category5Form" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Instructions -->
        <fieldset class="form-group">
          <div class="row">
            <label for="lable" class="col-sm-10 col-form-label">Upload a confirmation letter from Faculty of Graduate Studies/respective authority on supervising or intending to supervise postgraduate students engaged in clinical or research work in the current year <span class="text-danger">*</span></label>
          </div>
        </fieldset>

        <!-- Upload File -->
        <div class="form-group row">
          <div class="col-sm-10">
            <input type="file" name="cat5UploadFile" class="form-control" id="cat5UploadFile" required accept=".pdf">
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check"></i> Submit Category 5
            </button>
          </div>
        </div>
      </form>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category5Form').reset();
}
</script>

@endsection
