<?php
$prefix = Request::route()->getPrefix();
$route = Route::current()->getName();

?>
<aside class="main-sidebar sidebar-dark-primary elevation-4">
  <!-- Brand Logo -->
  <a href="" class="brand-link">
    <img src="<?php echo e(asset('backend/dist/img/top.png')); ?>" alt="AdminLTE Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
    <span class="brand-text font-weight-light">
      <h3 style="color:white"><b>USJ</b>Research</h3>
      
  </a>

  <!-- Sidebar -->
  <div class="sidebar">
    <!-- Sidebar user panel (optional) -->
    <div class="user-panel mt-2 pb-1 mb-1 d-flex">
      <div class="image">

        <img src="<?php echo e(asset('backend/dist/img/profile.jpg')); ?>" class="" alt="User Image">

      </div>
      <div class="info">
        <a href="" class="d-block">
          <?php if(auth()->guard()->check()): ?>
          <?php echo e(strtoupper(auth()->user()->name)); ?>

          <?php endif; ?>
        </a>
        <span class="text-white" style="font-size: 12px">
          <i class="nav-icon far fa-circle text-success"></i>&nbsp; Online</span>
      </div>
    </div>

    <!-- SidebarSearch Form -->
    

    <!-- Sidebar Menu -->
    <nav class="mt-2">
      <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
        <!-- Add icons to the links using the .nav-icon class
               with font-awesome or any other icon font library -->
        <li class="nav-item menu-open">
          <a href="<?php echo e(route('dashboard')); ?>" class="nav-link <?php echo e($route == 'dashboard'? 'active': ''); ?>">
            <i class="nav-icon fas fa-tachometer-alt"></i>
            <p>
              Dashboard
            </p>
          </a>

        </li>

        
        <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin')): ?>
        <li class="nav-item">
          <a href="#" class="nav-link <?php echo e($prefix == '/profile'? 'active': ''); ?>">
            <i class="nav-icon fas fa-user-plus"></i>
            <p>
              Manage Profile
              <i class="fas fa-angle-left right"></i>
              
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('profile.view')); ?>" class="nav-link <?php echo e($route == 'profile.view'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Your Profile</p>
              </a>
            </li>
          </ul>
        </li>
        <?php endif; ?>
        <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin')): ?>
        <li class="nav-item">
          <a href="#" class="nav-link <?php echo e($prefix == '/user'? 'active': ''); ?>">
            <i class="nav-icon fas fa-users"></i>
            <p>
              Manage Users
              <i class="fas fa-angle-left right"></i>
              
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('user.view')); ?>" class="nav-link <?php echo e($route == 'user.view'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>View Users</p>
              </a>
            </li>
            <li class="nav-item">
              <a href="<?php echo e(route('user.add.view')); ?>" class="nav-link <?php echo e($route == 'user.add.view'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Add New User</p>
              </a>
            </li>
          </ul>
        </li>
        <?php endif; ?>
        <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin')): ?>
        <li class="nav-item">
          <a href="#" class="nav-link <?php echo e($prefix == '/role'? 'active': ''); ?>">
            <i class="nav-icon fa fa-id-badge"></i>
            <p>
              Role
              <i class="fas fa-angle-left right"></i>
              
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('role.index')); ?>" class="nav-link <?php echo e($route == 'role.index'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Role List</p>
              </a>
            </li>
            <li class="nav-item">
              <a href="<?php echo e(route('role.add')); ?>" class="nav-link <?php echo e($route == 'role.add'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Role Add</p>
              </a>
            </li>
          </ul>
        </li>
        <?php endif; ?>
        <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin1')): ?>
        <li class="nav-item">
          <a href="#" class="nav-link <?php echo e($prefix == '/permission'? 'active': ''); ?>">
            <i class="nav-icon fa fa-snowflake"></i>
            <p>
              Permission
              <i class="fas fa-angle-left right"></i>
              
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('permission.index')); ?>" class="nav-link <?php echo e($route == 'permission.index'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Permission List</p>
              </a>
            </li>
            <li class="nav-item">
              <a href="<?php echo e(route('permission.add')); ?>" class="nav-link <?php echo e($route == 'permission.add'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Permission Add</p>
              </a>
            </li>
          </ul>
        </li>
        <?php endif; ?>
        <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin|sc|ra|cochair')): ?>
        <li class="nav-item">
          <a href="#" class="nav-link <?php echo e($prefix == '/application'? 'active': ''); ?>">
            <i class="nav-icon fa fa-folder-open"></i>
            <p>
              Application
              <i class="fas fa-angle-left right"></i>
              
            </p>
          </a>
          <ul class="nav nav-treeview">
            <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin|sc|ra')): ?>
            <li class="nav-item">
              <a href="<?php echo e(route('app.show')); ?>" class="nav-link <?php echo e($route == 'app.show'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>1st Check</p>
              </a>
            </li>
            <?php endif; ?>
            <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin|sc|ra')): ?>
            <li class="nav-item">
              <a href="<?php echo e(route('app.summary')); ?>" class="nav-link <?php echo e($route == 'app.summary'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Application Summary</p>
              </a>
            </li>
            <?php endif; ?>
            <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin')): ?>
            <li class="nav-item">
              <a href="<?php echo e(route('cat7.proposal.list')); ?>" class="nav-link <?php echo e($route == 'cat7.proposal.list'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Proposal List</p>
              </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo e(route('cat7.summary.list')); ?>" class="nav-link <?php echo e($route == 'cat7.summary.list'? 'active': ''); ?>">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Category 7 List</p>
                </a>
              </li>
            <?php endif; ?>
          </ul>
        </li>
        <?php endif; ?>
        <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin')): ?>
        <li class="nav-item">
          <a href="#" class="nav-link <?php echo e($prefix == '/librarian'? 'active': ''); ?>">
            <i class="nav-icon fa fa-university"></i>
            <p>
              Librarian
              <i class="fas fa-angle-left right"></i>
              
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('librarian.list.open')); ?>" class="nav-link <?php echo e($route == 'librarian.list.open'? 'active': ''); ?>" target="_blank">
                <i class="far fa-circle nav-icon"></i>
                <p>Pending list</p>
              </a>
            </li>
          </ul>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('librarian.approved.list')); ?>" class="nav-link <?php echo e($route == 'librarian.approved.list'? 'active': ''); ?>" >
                <i class="far fa-circle nav-icon"></i>
                <p>Approved list</p>
              </a>
            </li>
          </ul>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('librarian.rejected.list')); ?>" class="nav-link <?php echo e($route == 'librarian.rejected.list'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Rejected list</p>
              </a>
            </li>
          </ul>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('cat1.approved.list.all')); ?>" class="nav-link <?php echo e($route == 'cat1.approved.list.all'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Category 1 list</p>
              </a>
            </li>
          </ul>
        </li>
        <?php endif; ?>
        <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin')): ?>
        <li class="nav-item">
          <a href="#" class="nav-link <?php echo e($prefix == '/cochair'? 'active': ''); ?>">
            <i class="nav-icon fa fa-tty"></i>
            <p>
              Co-Chair
              <i class="fas fa-angle-left right"></i>
              
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('app.cochair.view')); ?>" class="nav-link <?php echo e($route == 'app.cochair.view'? 'active': ''); ?>" target="_blank">
                <i class="far fa-circle nav-icon"></i>
                <p>Pending list</p>
              </a>
            </li>
          </ul>
        </li>
        <?php endif; ?>
        <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin')): ?>
        <li class="nav-item">
          <a href="#" class="nav-link <?php echo e($prefix == '/vc'? 'active': ''); ?>">
            <i class="nav-icon fa fa-universal-access"></i>
            <p>
                Vice Chancellor
              <i class="fas fa-angle-left right"></i>
              
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('app.vc.view')); ?>" class="nav-link <?php echo e($route == 'app.vc.view'? 'active': ''); ?>" target="_blank">
                <i class="far fa-circle nav-icon"></i>
                <p>Pending list</p>
              </a>
            </li>
          </ul>
        </li>
        <?php endif; ?>
        <?php if (\Illuminate\Support\Facades\Blade::check('role', 'admin|sc|ra')): ?>
        <li class="nav-item">
          <a href="#" class="nav-link <?php echo e($prefix == '/final'? 'active': ''); ?>">
            <i class="nav-icon fa fa-book"></i>
            <p>
                Summary Report
              <i class="fas fa-angle-left right"></i>
              
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="<?php echo e(route('app.process')); ?>" class="nav-link <?php echo e($route == 'app.process'? 'active': ''); ?>">
                <i class="far fa-circle nav-icon"></i>
                <p>Application Process</p>
              </a>
            </li>
            
            <li class="nav-item">
                <a href="<?php echo e(route('app.approved.list.all')); ?>" class="nav-link <?php echo e($route == 'app.approved.list.all'? 'active': ''); ?>">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Approved All List</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="<?php echo e(route('app.rejected.list.all')); ?>" class="nav-link <?php echo e($route == 'app.rejected.list.all'? 'active': ''); ?>">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Rejected All List</p>
                </a>
              </li>
          </ul>

          
        </li>
        <?php endif; ?>


        <li class="nav-header"></li>
        <li class="nav-item ">
          <a type="button" class="nav-link bg-danger" data-toggle="modal" data-target="#exampleModalCenter">
            <i class="nav-icon fas fa-power-off text-white"></i>
            <p class="text">Logout</p>
          </a>
        </li>
      </ul>
    </nav>
    <!-- /.sidebar-menu -->
  </div>
  <!-- /.sidebar -->
</aside>
<div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalCenterTitle">User Logout</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <h6>Are you realy want to logout system?</h6>
      </div>
      <div class="modal-footer">
        <a type="button" class="btn btn-rounded btn-danger" data-dismiss="modal">Cancel</a>
        <?php if(app()->environment('production')): ?>
        <form method="get" action="https://usjnetsso.sjp.ac.lk/sso/user_logout">
          <?php echo csrf_field(); ?>
          <button type="submit" class="btn btn-rounded btn-primary">Logout</button>
        </form>
        <?php else: ?>
        <form method="post" action="<?php echo e(route('logout')); ?>">
          <?php echo csrf_field(); ?>
          <button type="submit" class="btn btn-rounded btn-primary">Logout</button>
        </form>
         <?php endif; ?>
      </div>
    </div>
  </div>
</div>
<?php /**PATH D:\Development\research_allowness\resources\views/admin/body/sidebar.blade.php ENDPATH**/ ?>