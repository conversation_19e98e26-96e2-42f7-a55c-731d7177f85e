<?php $__env->startSection('admin'); ?>

<section class="content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title" style="color: #A82727; font-weight: bold;">
        <i class="fa fa-file-text-o"></i> Category 1: Indexed Journal Publications (Consecutive)
      </h3>
      <div class="card-tools">
        <a href="<?php echo e(route('application.category.list')); ?>" class="btn btn-secondary btn-sm">
          <i class="fa fa-arrow-left"></i> Back to Categories
        </a>
      </div>
    </div>

    <div class="card-body">
      <form id="category1aForm" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <!-- Instructions -->
        <div class="alert alert-warning">
          <h5><i class="icon fa fa-exclamation-triangle"></i> Consecutive Publications</h5>
          This category is for applicants who have consecutive indexed journal publications from previous years.
        </div>

        <!-- Previous Publication Reference -->
        <div class="form-group row">
          <label for="previousPublicationRef" class="col-sm-3 col-form-label">
            Previous Publication Reference <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="previousPublicationRef" name="previous_publication_ref"
                   placeholder="Enter reference number of previous publication" required>
          </div>
        </div>

        <!-- Publication Title -->
        <div class="form-group row">
          <label for="publicationTitle" class="col-sm-3 col-form-label">
            Publication Title <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="publicationTitle" name="publication_title"
                   placeholder="Enter the title of your publication" required>
          </div>
        </div>

        <!-- Journal Name -->
        <div class="form-group row">
          <label for="journalName" class="col-sm-3 col-form-label">
            Journal Name <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="journalName" name="journal_name"
                   placeholder="Enter the journal name" required>
          </div>
        </div>

        <!-- Indexing Service -->
        <div class="form-group row">
          <label for="indexingService" class="col-sm-3 col-form-label">
            Indexing Service <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="indexingService" name="indexing_service" required>
              <option value="">Select Indexing Service</option>
              <option value="SCI">SCI (Science Citation Index)</option>
              <option value="SSCI">SSCI (Social Sciences Citation Index)</option>
              <option value="AHCI">AHCI (Arts & Humanities Citation Index)</option>
              <option value="Scopus">Scopus</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>

        <!-- Consecutive Year -->
        <div class="form-group row">
          <label for="consecutiveYear" class="col-sm-3 col-form-label">
            Consecutive Year <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <select class="form-control" id="consecutiveYear" name="consecutive_year" required>
              <option value="">Select Year</option>
              <option value="2">2nd Consecutive Year</option>
              <option value="3">3rd Consecutive Year</option>
              <option value="4">4th Consecutive Year</option>
              <option value="5">5th+ Consecutive Year</option>
            </select>
          </div>
        </div>

        <!-- Volume, Issue, Pages -->
        <div class="form-group row">
          <label for="volumeIssuePages" class="col-sm-3 col-form-label">
            Volume, Issue & Pages <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="text" class="form-control" id="volumeIssuePages" name="volume_issue_pages"
                   placeholder="e.g., Vol. 15(2), pp. 123-135" required>
          </div>
        </div>

        <!-- Publication File Upload -->
        <div class="form-group row">
          <label for="publicationFile" class="col-sm-3 col-form-label">
            Publication File <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="publicationFile" name="publication_file"
                   accept=".pdf,.doc,.docx" required>
            <small class="form-text text-muted">Upload PDF, DOC, or DOCX file (Max: 10MB)</small>
          </div>
        </div>

        <!-- Evidence File Upload -->
        <div class="form-group row">
          <label for="evidenceFile" class="col-sm-3 col-form-label">
            Indexing Evidence <span class="text-danger">*</span>
          </label>
          <div class="col-sm-9">
            <input type="file" class="form-control-file" id="evidenceFile" name="evidence_file"
                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
            <small class="form-text text-muted">
              Upload evidence of indexing (PDF, DOC, DOCX, JPG, PNG) - Max: 5MB
            </small>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-group row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-warning mr-2" onclick="clearForm()">
              <i class="fa fa-eraser"></i> Clear
            </button>
            <button type="button" class="btn btn-info mr-2" onclick="saveDraft()">
              <i class="fa fa-save"></i> Save as Draft
            </button>
            <button type="submit" class="btn btn-success">
              <i class="fa fa-plus"></i> Add Publication
            </button>
          </div>
        </div>
      </form>

      <!-- Publications List -->
      <div class="mt-4">
        <h5>Added Consecutive Publications</h5>
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead class="thead-dark">
              <tr>
                <th>No.</th>
                <th>Title</th>
                <th>Journal</th>
                <th>Consecutive Year</th>
                <th>Volume/Issue/Pages</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="consecutivePublicationsTableBody">
              <tr>
                <td colspan="6" class="text-center text-muted">No consecutive publications added yet</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</section>

<script>
function clearForm() {
    document.getElementById('category1aForm').reset();
}

function saveDraft() {
    alert('Draft saved successfully!');
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Development\research_allowness\resources\views/frontend/application_new/component/category1a.blade.php ENDPATH**/ ?>